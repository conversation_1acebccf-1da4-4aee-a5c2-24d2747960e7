#!/usr/bin/env python3
"""
Test script to verify the license dialog initialization fix.
This tests that the dialog can be created without AttributeError.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from ui.main_window import LicenseActivationDialog
from license_manager import LicenseManager
from config_manager import ConfigManager

def test_license_dialog_creation():
    """Test that the license dialog can be created without errors."""
    
    print("Testing License Dialog Creation Fix")
    print("=" * 50)
    
    # Create QApplication (required for Qt widgets)
    app = QApplication(sys.argv)
    
    try:
        # Create config manager and license manager
        config_manager = ConfigManager()
        license_manager = LicenseManager(config_manager)
        
        print("✅ Config and License managers created successfully")
        
        # Try to create the license dialog (this was causing the AttributeError)
        dialog = LicenseActivationDialog(None, license_manager)
        
        print("✅ License dialog created successfully")
        print("✅ No AttributeError occurred!")
        
        # Test that the delete button exists and has correct visibility
        if hasattr(dialog, 'delete_button'):
            print("✅ Delete button exists")
            
            # Check if button visibility is set correctly
            verification = license_manager.verify_license()
            has_license = verification.get('valid', False)
            
            if has_license:
                print(f"✅ License is active, delete button should be visible: {dialog.delete_button.isVisible()}")
            else:
                print(f"✅ No license active, delete button should be hidden: {not dialog.delete_button.isVisible()}")
        else:
            print("❌ Delete button does not exist")
        
        # Test the update methods
        try:
            dialog._update_status_display()
            print("✅ _update_status_display() works correctly")
        except Exception as e:
            print(f"❌ _update_status_display() failed: {e}")
        
        try:
            dialog._update_button_visibility()
            print("✅ _update_button_visibility() works correctly")
        except Exception as e:
            print(f"❌ _update_button_visibility() failed: {e}")
        
        print("\n" + "=" * 50)
        print("License Dialog Test Complete - All checks passed!")
        
        return True
        
    except AttributeError as e:
        print(f"❌ AttributeError still occurs: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_license_dialog_creation()
    sys.exit(0 if success else 1)
