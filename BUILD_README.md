# BulkAI Desktop Application - Build Instructions

This document explains how to build a standalone executable (.exe) from the BulkAI desktop application.

## Prerequisites

1. **Python 3.8+** installed and available in PATH
2. **PyInstaller** installed: `pip install pyinstaller`
3. **All project dependencies** installed: `pip install -r requirements.txt`

## Quick Start

### Option 1: Using the Batch File (Windows)
```bash
# Simply double-click or run:
build.bat
```

### Option 2: Using the Python Script Directly
```bash
# Basic build
python build_executable.py

# Build with cleanup of temporary files
python build_executable.py --clean

# Build without creating backup of spec file
python build_executable.py --no-backup
```

## What the Build Script Does

1. **Prerequisites Check**: Verifies PyInstaller is installed and all required files exist
2. **Spec File Backup**: Creates a backup of the original `bulky.spec` file
3. **Spec File Update**: Configures the spec file for standalone distribution (--onefile mode)
4. **Clean Previous Builds**: Removes old build artifacts
5. **Run PyInstaller**: Builds the executable using the updated spec file
6. **Verify Output**: Checks that the executable was created successfully
7. **Optional Cleanup**: Removes temporary build files (if `--clean` flag is used)

## Output

The build process creates:
- **`dist/Azanx Bulk AI Images.exe`** - The standalone executable
- **`bulky.spec.backup`** - Backup of the original spec file (if it didn't exist)

## Key Features of the Standalone Executable

- **Single File**: Everything bundled into one .exe file
- **No Dependencies**: Runs on Windows without requiring Python or any libraries
- **All Resources Included**: Icons, config files, and other assets are embedded
- **Ready for Distribution**: Can be shared with end users directly

## Troubleshooting

### Common Issues

1. **"PyInstaller is not installed"**
   ```bash
   pip install pyinstaller
   ```

2. **"Module not found" errors during build**
   ```bash
   pip install -r requirements.txt
   ```

3. **Large executable size**
   - This is normal for PyQt6 applications (typically 100-200MB)
   - The size includes the Python interpreter and all dependencies

4. **Build fails with import errors**
   - Check that all dependencies are properly installed
   - Verify that the application runs correctly before building: `python main.py`

### Advanced Options

You can manually edit `bulky.spec` for advanced customization:
- Add/remove hidden imports
- Include additional data files
- Modify executable properties
- Change compression settings

### Manual Build (Alternative)

If the script doesn't work, you can build manually:
```bash
# Clean previous builds
rmdir /s dist build

# Run PyInstaller
python -m PyInstaller --clean bulky.spec
```

## File Structure After Build

```
project/
├── dist/
│   └── Azanx Bulk AI Images.exe    # ← Your standalone executable
├── build/                          # Temporary files (can be deleted)
├── bulky.spec                      # Updated spec file
├── bulky.spec.backup              # Original spec file backup
└── build_executable.py            # Build script
```

## Testing the Executable

After building, you can test the executable:

```bash
# Test if the executable launches correctly
python test_executable.py
```

This will:
- Verify the executable exists
- Check file size
- Launch the executable briefly to ensure it starts without crashing
- Terminate it cleanly

## Distribution

The executable in `dist/Azanx Bulk AI Images.exe` is completely standalone and can be:
- Copied to any Windows computer
- Distributed to end users
- Run without installing Python or any dependencies
- Packaged into an installer if desired

## Build Results

A successful build typically produces:
- **File size**: ~50-60 MB (normal for PyQt6 applications)
- **Build time**: 30-60 seconds depending on system
- **Dependencies**: All bundled (PyQt6, PIL, requests, runware, etc.)

## Notes

- The first run of the executable may be slower as it extracts temporary files
- Windows Defender might flag the executable initially (common with PyInstaller)
- The executable includes all necessary DLLs and Python libraries
- Generated images will be saved in a `generated_images` folder next to the executable
- Icon support requires `cairosvg` package: `pip install cairosvg`
