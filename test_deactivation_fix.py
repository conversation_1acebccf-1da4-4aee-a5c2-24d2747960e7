#!/usr/bin/env python3
"""
Test script for the fixed license deactivation endpoint.
This tests the improved error handling and database migration.
"""

import requests
import json
import platform
import hashlib

def test_deactivation_fix():
    """Test the fixed license deactivation endpoint."""
    
    # Configuration
    license_server_url = "https://bulkimages.azanx.com/licensing/api"
    test_license_key = "BULKAI-67BD2EA2-CE1423CF-40039D04"
    
    # Generate a test device ID
    hardware_string = f"{platform.machine()}-{platform.processor()}-{platform.system()}-{platform.node()}"
    device_id = hashlib.sha256(hardware_string.encode()).hexdigest()[:16]
    
    print(f"Testing Fixed License Deactivation")
    print("=" * 60)
    print(f"Server URL: {license_server_url}")
    print(f"Device ID: {device_id}")
    print(f"License Key: {test_license_key}")
    print()
    
    # Test 1: Run database migration (if accessible)
    print("1. Testing Database Migration")
    print("-" * 40)
    try:
        migration_url = license_server_url.replace('/api', '/database/migrate.php')
        response = requests.get(migration_url, timeout=30)
        
        print(f"Migration Status Code: {response.status_code}")
        if response.status_code == 200:
            print("Migration Response:")
            print(response.text)
        else:
            print(f"Migration not accessible or failed: {response.status_code}")
    except Exception as e:
        print(f"Migration test skipped: {e}")
    
    # Test 2: Ensure license is activated first
    print("\n2. Ensuring License is Activated")
    print("-" * 40)
    try:
        device_info = {
            'system': platform.system(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version()
        }
        
        response = requests.post(
            f"{license_server_url}/?action=activate-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id,
                'device_name': platform.node(),
                'device_info': json.dumps(device_info)
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Activation Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Activation Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'No message')}")
        else:
            print(f"Activation failed: {response.text}")
            
    except Exception as e:
        print(f"Activation error: {e}")
    
    # Test 3: Test the fixed deactivation endpoint
    print("\n3. Testing Fixed Deactivation Endpoint")
    print("-" * 40)
    try:
        response = requests.post(
            f"{license_server_url}/?action=deactivate-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Deactivation Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Deactivation Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'No message')}")
            if 'deactivation_date' in result:
                print(f"Deactivation Date: {result['deactivation_date']}")
            
            if result.get('success'):
                print("🎉 License deactivation is now working correctly!")
            else:
                print(f"⚠️ Deactivation returned success=false: {result.get('error', 'Unknown error')}")
                
        else:
            try:
                error_response = response.json()
                print(f"❌ Deactivation failed with status {response.status_code}")
                print(f"Error: {error_response.get('error', 'Unknown error')}")
                
                if 'debug_info' in error_response:
                    print(f"Debug Info: {error_response['debug_info']}")
                    print("This debug info will help identify the specific database issue.")
                    
            except:
                print(f"❌ Deactivation failed: {response.text}")
                
    except Exception as e:
        print(f"❌ Deactivation request error: {e}")
    
    # Test 4: Verify license status after deactivation
    print("\n4. Verifying License Status After Deactivation")
    print("-" * 40)
    try:
        response = requests.post(
            f"{license_server_url}/?action=verify-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Verification Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"License Valid: {result.get('valid', False)}")
            
            device_info = result.get('device', {})
            print(f"Device Activated: {device_info.get('activated', False)}")
            
            if not result.get('valid') or not device_info.get('activated'):
                print("✅ License properly deactivated - device no longer has access")
            else:
                print("⚠️ License still appears to be active after deactivation")
        else:
            print(f"Verification error: {response.text}")
            
    except Exception as e:
        print(f"Verification error: {e}")
    
    # Test 5: Test error handling with invalid data
    print("\n5. Testing Error Handling")
    print("-" * 40)
    
    # Test with missing license key
    try:
        response = requests.post(
            f"{license_server_url}/?action=deactivate-license",
            json={
                'device_id': device_id
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Missing license key test - Status: {response.status_code}")
        if response.status_code == 400:
            result = response.json()
            print(f"✅ Proper error handling: {result.get('error', 'No error message')}")
        else:
            print(f"⚠️ Unexpected response: {response.text}")
            
    except Exception as e:
        print(f"Error handling test failed: {e}")
    
    print("\n" + "=" * 60)
    print("Deactivation Fix Test Complete!")
    print("\nIf you see debug_info in the error responses, that indicates")
    print("the specific database issue that needs to be resolved.")

if __name__ == "__main__":
    test_deactivation_fix()
