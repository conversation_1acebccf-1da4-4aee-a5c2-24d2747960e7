@echo off
echo ================================================================
echo BulkAI Desktop Application - Comprehensive Build Script
echo ================================================================
echo.

REM Check if Python is available
echo [CHECK] Verifying Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again.
    pause
    exit /b 1
)

REM Display Python version
for /f "tokens=*" %%i in ('python --version') do echo [OK] Found %%i

REM Check if PyInstaller is installed
echo [CHECK] Verifying PyInstaller installation...
python -m PyInstaller --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] PyInstaller not found. Installing...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo [ERROR] Failed to install PyInstaller
        pause
        exit /b 1
    )
    echo [OK] PyInstaller installed successfully
) else (
    for /f "tokens=*" %%i in ('python -m PyInstaller --version') do echo [OK] PyInstaller version %%i found
)

REM Check if all dependencies are installed
echo [CHECK] Verifying project dependencies...
python -c "import PyQt6, PIL, requests, keyring, cryptography" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Some dependencies missing. Installing from requirements.txt...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
    echo [OK] Dependencies installed successfully
) else (
    echo [OK] All required dependencies found
)

REM Check if main application files exist
echo [CHECK] Verifying application files...
if not exist "main.py" (
    echo [ERROR] main.py not found
    pause
    exit /b 1
)
if not exist "config.json" (
    echo [ERROR] config.json not found
    pause
    exit /b 1
)
if not exist "ui\main_window.py" (
    echo [ERROR] ui\main_window.py not found
    pause
    exit /b 1
)
echo [OK] All required application files found

REM Run the comprehensive build script
echo.
echo [BUILD] Starting build process...
echo This may take several minutes depending on your system...
echo.
python build_executable_simple.py --clean

if errorlevel 1 (
    echo.
    echo [ERROR] Build failed! Check the output above for details.
    pause
    exit /b 1
)

echo.
echo ================================================================
echo [SUCCESS] Build completed successfully!
echo ================================================================
echo.
echo The executable has been created in the 'dist' folder.
echo You can now distribute 'dist\Pixeliano - Unlimited AI Images. One Click Away.exe' to end users.
echo.
echo [TIP] Test the executable before distribution:
echo       cd dist
echo       "Pixeliano - Unlimited AI Images. One Click Away.exe"
echo.
pause
