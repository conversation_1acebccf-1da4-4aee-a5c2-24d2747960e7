#!/usr/bin/env python3
"""
Verification script to confirm the license activation fix is working.
This simulates the exact same calls that the desktop app will make.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from license_manager import LicenseManager
from config_manager import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>

def test_license_manager():
    """Test the license manager with the fixed URLs."""
    
    print("Testing License Manager with Fixed URLs")
    print("=" * 50)
    
    # Create a test config manager
    config_manager = ConfigManager()
    
    # Create license manager (this will use the fixed URLs)
    license_manager = LicenseManager(config_manager)
    
    print(f"License Server URL: {license_manager.license_server_url}")
    print(f"Device ID: {license_manager.device_id}")
    
    # Test 1: License Verification
    print("\n1. Testing License Verification")
    print("-" * 30)
    
    # Test with no license key first
    result = license_manager.verify_license()
    print(f"No License Key - Valid: {result.get('valid', False)}")
    print(f"No License Key - Plan Type: {result.get('plan_type', 'unknown')}")
    
    # Test 2: License Activation (with test key)
    print("\n2. Testing License Activation")
    print("-" * 30)
    
    test_license_key = "BULKAI-67BD2EA2-CE1423CF-40039D04"  # Use the working test key
    
    result = license_manager.activate_license(test_license_key)
    print(f"Activation Success: {result.get('success', False)}")
    print(f"Activation Message: {result.get('message', 'No message')}")
    
    if result.get('success'):
        print("✅ License activation is working!")
        
        # Test 3: Verify the activated license
        print("\n3. Testing License Verification After Activation")
        print("-" * 30)
        
        verification = license_manager.verify_license()
        print(f"License Valid: {verification.get('valid', False)}")
        
        if verification.get('valid'):
            license_info = verification.get('license', {})
            print(f"Plan Name: {license_info.get('plan_name', 'Unknown')}")
            print(f"Plan Type: {license_info.get('plan_type', 'Unknown')}")
            print(f"Daily Limit: {license_info.get('daily_image_limit', 'Unknown')}")
            
            usage_info = verification.get('usage', {})
            print(f"Today's Usage: {usage_info.get('today_usage', 0)}")
            print(f"Daily Limit Exceeded: {usage_info.get('daily_limit_exceeded', False)}")
            
        # Test 4: Usage Tracking
        print("\n4. Testing Usage Tracking")
        print("-" * 30)
        
        license_manager.track_usage(
            images_generated=1,
            api_calls=1,
            provider_used="together_ai",
            model_used="flux_schnell"
        )
        print("✅ Usage tracking completed (check logs for success/failure)")
        
        # Test 5: Plan Status Methods
        print("\n5. Testing Plan Status Methods")
        print("-" * 30)
        
        print(f"Is Trial Plan: {license_manager.is_trial_plan()}")
        print(f"Is Pro Plan: {license_manager.is_pro_plan()}")
        print(f"Can Access Bulk Generation: {license_manager.can_access_bulk_generation()}")
        print(f"Can Access Runware AI: {license_manager.can_access_runware_ai()}")
        print(f"Usage Status Text: {license_manager.get_usage_status_text()}")
        print(f"Plan Status Text: {license_manager.get_plan_status_text()}")
        
    else:
        print("❌ License activation failed!")
        print(f"Error: {result.get('error', 'Unknown error')}")
    
    print("\n" + "=" * 50)
    print("License Manager Test Complete!")

if __name__ == "__main__":
    test_license_manager()
