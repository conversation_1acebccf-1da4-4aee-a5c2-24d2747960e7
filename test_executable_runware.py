#!/usr/bin/env python3
"""
Test script to verify that the executable properly loads Runware AI models.
This script tests the executable by launching it and checking if Runware models are available.
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def test_executable_exists():
    """Check if the executable exists."""
    exe_path = Path("dist/Pixeliano - Unlimited AI Images. One Click Away.exe")
    if exe_path.exists():
        print(f"✓ Executable found: {exe_path}")
        print(f"  File size: {exe_path.stat().st_size / (1024*1024):.1f} MB")
        return True
    else:
        print(f"✗ Executable not found: {exe_path}")
        return False

def test_executable_launch():
    """Test if the executable can launch without errors."""
    print("\nTesting executable launch...")
    exe_path = Path("dist/Pixeliano - Unlimited AI Images. One Click Away.exe")
    
    try:
        # Launch the executable with a timeout
        print("  Launching executable (will timeout after 10 seconds)...")
        process = subprocess.Popen(
            [str(exe_path)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for the app to start
        time.sleep(10)
        
        # Check if process is still running
        if process.poll() is None:
            print("  ✓ Executable launched successfully and is running")
            # Terminate the process
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
        else:
            # Process exited, check for errors
            stdout, stderr = process.communicate()
            print(f"  ✗ Executable exited with code: {process.returncode}")
            if stdout:
                print(f"  STDOUT: {stdout}")
            if stderr:
                print(f"  STDERR: {stderr}")
            return False
            
    except Exception as e:
        print(f"  ✗ Error launching executable: {e}")
        return False

def check_config_in_executable():
    """Check if config.json is properly bundled with the executable."""
    print("\nChecking if config.json is bundled...")
    
    # The config should be bundled inside the executable
    # We can't directly check this, but we can verify the source config exists
    config_path = Path("config.json")
    if config_path.exists():
        print(f"  ✓ Source config.json exists: {config_path}")
        
        # Check if it has the api_providers structure
        try:
            import json
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            if 'api_providers' in config_data:
                providers = config_data['api_providers']
                print(f"  ✓ Config has api_providers with {len(providers)} providers")
                
                if 'runware_ai' in providers:
                    runware_models = providers['runware_ai'].get('models', [])
                    print(f"  ✓ Runware AI has {len(runware_models)} models configured")
                    
                    # Show first few models
                    for i, model in enumerate(runware_models[:3]):
                        print(f"    - {model.get('name', 'Unknown')} ({model.get('id', 'Unknown ID')})")
                    if len(runware_models) > 3:
                        print(f"    ... and {len(runware_models) - 3} more models")
                    
                    return True
                else:
                    print("  ✗ No runware_ai provider found in config")
                    return False
            else:
                print("  ✗ Config missing api_providers section")
                return False
                
        except Exception as e:
            print(f"  ✗ Error reading config: {e}")
            return False
    else:
        print(f"  ✗ Source config.json not found: {config_path}")
        return False

def main():
    """Main test function."""
    print("BulkAI Executable - Runware Models Test")
    print("=" * 50)
    print("This script tests if the executable properly includes Runware AI models.")
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Check if executable exists
    if test_executable_exists():
        success_count += 1
    
    # Test 2: Check config bundling
    if check_config_in_executable():
        success_count += 1
    
    # Test 3: Test executable launch
    if test_executable_launch():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✓ All tests passed! The executable should have Runware models available.")
        print("\nTo verify Runware models in the executable:")
        print("1. Launch the executable: dist\\Pixeliano - Unlimited AI Images. One Click Away.exe")
        print("2. Go to Settings > API Key Manager")
        print("3. Set a Runware AI API key")
        print("4. Select 'Runware AI' as the provider")
        print("5. Check if models appear in the model dropdown")
    else:
        print("✗ Some tests failed. The executable may not work correctly.")
        
        if success_count == 0:
            print("\nTroubleshooting:")
            print("1. Make sure you've run the build script: python build_executable_simple.py --clean")
            print("2. Check that all dependencies are installed: pip install -r requirements.txt")
            print("3. Verify the source code is working: python main.py")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
