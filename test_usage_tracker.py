#!/usr/bin/env python3
"""
Test script to demonstrate the real-time image generation tracking functionality.

This script tests the usage tracker with different scenarios including:
1. Normal usage tracking
2. Trial plan limit enforcement
3. Real-time updates
4. Daily reset functionality
"""

import sys
import os
import json
import datetime
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from usage_tracker import UsageTracker
from config_manager import <PERSON>fig<PERSON><PERSON><PERSON>
from license_manager import LicenseManager


class MockLicenseManager:
    """Mock license manager for testing trial plan functionality."""
    
    def __init__(self, daily_limit=10):
        self.daily_limit = daily_limit
        
    def get_daily_usage_limit(self):
        """Return the daily usage limit for testing."""
        return self.daily_limit
    
    def is_trial_plan(self):
        """Return True to simulate trial plan."""
        return True
    
    def get_plan_name(self):
        """Return plan name."""
        return "Trial"


def test_usage_tracker():
    """Test the usage tracker functionality."""
    print("=== Testing Usage Tracker ===\n")
    
    # Create mock objects
    config_manager = ConfigManager()
    
    # Test with trial plan (10 images per day)
    print("1. Testing Trial Plan (10 images/day limit)")
    mock_license_manager = MockLicenseManager(daily_limit=10)
    
    # Clean up any existing usage data for testing
    usage_file = Path("usage_data.json")
    if usage_file.exists():
        backup_file = Path("usage_data_backup.json")
        usage_file.rename(backup_file)
        print(f"   Backed up existing usage data to {backup_file}")
    
    try:
        # Create usage tracker
        usage_tracker = UsageTracker(config_manager, mock_license_manager)
        
        # Test initial state
        print(f"   Initial daily count: {usage_tracker.get_daily_count()}")
        print(f"   Daily limit: {usage_tracker.get_daily_limit()}")
        print(f"   Remaining: {usage_tracker.get_remaining_count()}")
        print(f"   Status text: {usage_tracker.get_usage_status_text()}")
        
        # Test image generation tracking
        print("\n2. Testing Image Generation Tracking")
        for i in range(12):  # Generate more than the limit
            can_generate, message = usage_tracker.can_generate_image()
            
            if can_generate:
                print(f"   Generation {i+1}: Allowed")
                usage_tracker.increment_usage()
                print(f"      New count: {usage_tracker.get_daily_count()}")
                print(f"      Remaining: {usage_tracker.get_remaining_count()}")
            else:
                print(f"   Generation {i+1}: BLOCKED - {message}")
                break
        
        # Test status information
        print("\n3. Testing Status Information")
        status = usage_tracker.get_detailed_status()
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        # Test with unlimited plan
        print("\n4. Testing Unlimited Plan")
        unlimited_license_manager = MockLicenseManager(daily_limit=-1)
        unlimited_tracker = UsageTracker(config_manager, unlimited_license_manager)
        
        print(f"   Daily limit: {unlimited_tracker.get_daily_limit()}")
        print(f"   Status text: {unlimited_tracker.get_usage_status_text()}")
        
        # Generate a few images
        for i in range(3):
            can_generate, message = unlimited_tracker.can_generate_image()
            print(f"   Generation {i+1}: {'Allowed' if can_generate else 'Blocked'}")
            if can_generate:
                unlimited_tracker.increment_usage()
                print(f"      New count: {unlimited_tracker.get_daily_count()}")
        
        print("\n5. Testing Daily Reset")
        # Manually trigger a reset by changing the date
        usage_tracker.usage_data["last_reset_date"] = "2025-05-26"  # Yesterday
        usage_tracker._check_daily_reset()
        print(f"   Count after reset: {usage_tracker.get_daily_count()}")
        print(f"   Can generate after reset: {usage_tracker.can_generate_image()[0]}")
        
        print("\n=== All Tests Completed Successfully ===")
        
    finally:
        # Restore backup if it exists
        backup_file = Path("usage_data_backup.json")
        if backup_file.exists():
            if usage_file.exists():
                usage_file.unlink()
            backup_file.rename(usage_file)
            print(f"\nRestored original usage data from backup")


if __name__ == "__main__":
    test_usage_tracker()
