import logging
import sys

# Configure the logger
logger = logging.getLogger('bulkai')
logger.setLevel(logging.DEBUG)

# Create console handler
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.DEBUG)

# Create formatter
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)

# Add handler to logger
logger.addHandler(console_handler)

def get_logger():
    """Get the application logger.
    
    Returns:
        logging.Logger: The application logger.
    """
    return logger
