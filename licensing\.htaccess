# BulkAI Licensing System .htaccess

# Enable rewrite engine
RewriteEngine On

# Redirect to setup if database doesn't exist
RewriteCond %{REQUEST_URI} !^/licensing/setup\.php
RewriteCond %{REQUEST_URI} !^/licensing/admin/
RewriteCond %{REQUEST_URI} !^/licensing/api/
RewriteCond %{DOCUMENT_ROOT}/licensing/database/licenses.db !-f
RewriteRule ^(.*)$ /licensing/setup.php [L,R=302]

# API routing - handle clean URLs for API endpoints
RewriteCond %{REQUEST_URI} ^/licensing/api/(.+)$
RewriteRule ^api/(.+)$ api/index.php/$1 [L,QSA]

# Admin panel - redirect to login if not authenticated
RewriteCond %{REQUEST_URI} ^/licensing/admin/$
RewriteRule ^admin/$ admin/index.php [L]

# Security - deny access to sensitive files
<Files "*.db">
    Order Allow,Deny
    Deny from all
</Files>

<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

# Set proper MIME types
AddType application/json .json

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
