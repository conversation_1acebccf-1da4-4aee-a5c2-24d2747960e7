# Azanx Bulk AI Images - Executable Version

This is the standalone executable version of Azanx Bulk AI Images application.

## Running the Application

There are two ways to run the application:

1. **Using the Batch File**:
   - Double-click on the `Run Azanx Bulk AI Images.bat` file

2. **Directly Running the Executable**:
   - Navigate to the `dist\Azanx Bulk AI Images` folder
   - Double-click on `Azanx Bulk AI Images.exe`

## Important Files

- **config.json**: Contains API keys and application settings
- **test_prompts.txt**: Contains sample prompts for bulk image generation
- **generated_images/**: Folder where generated images will be saved

## Distribution

To distribute this application to other users:

1. Copy the entire `dist\Azanx Bulk AI Images` folder
2. Include the `config.json` and `test_prompts.txt` files
3. Include the `Run Azanx Bulk AI Images.bat` file (optional)
4. Zip everything together and share

## Troubleshooting

If the application doesn't start:

1. Make sure all files are in their correct locations
2. Check if the `config.json` file is properly formatted
3. Ensure that the `generated_images` folder exists (or the application will create it)
4. If you get any error messages, please report them

## Note on API Keys

The application uses API keys stored in the `config.json` file. If you distribute this application, users will need to update the API keys with their own.
