#!/usr/bin/env python3
"""
Test script for the Modern UI implementation.

This script tests the modern UI components and layout to ensure they work correctly
before integrating with the full application functionality.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from modernui import main
    
    if __name__ == "__main__":
        print("Testing BulkAI Modern UI...")
        print("This will launch the modern UI with placeholder functionality.")
        print("You can test the visual design and basic interactions.")
        print()
        
        # Run the modern UI
        main()
        
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all required dependencies are installed:")
    print("- PyQt6")
    print("- All BulkAI modules (config_manager, app_controller, etc.)")
    sys.exit(1)
    
except Exception as e:
    print(f"Error running modern UI: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
