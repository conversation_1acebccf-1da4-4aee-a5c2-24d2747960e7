<?php
/**
 * Installation script for BulkAI Licensing System
 * This script checks requirements and sets up the system
 */

$errors = [];
$warnings = [];
$success = [];

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    $errors[] = 'PHP 7.4 or higher is required. Current version: ' . PHP_VERSION;
} else {
    $success[] = 'PHP version: ' . PHP_VERSION . ' ✓';
}

// Check SQLite support
if (!extension_loaded('sqlite3')) {
    $errors[] = 'SQLite3 extension is not loaded';
} else {
    $success[] = 'SQLite3 extension loaded ✓';
}

// Check PDO SQLite support
if (!extension_loaded('pdo_sqlite')) {
    $errors[] = 'PDO SQLite extension is not loaded';
} else {
    $success[] = 'PDO SQLite extension loaded ✓';
}

// Check if database directory is writable
$db_dir = __DIR__ . '/database';
if (!is_dir($db_dir)) {
    if (!mkdir($db_dir, 0755, true)) {
        $errors[] = 'Cannot create database directory: ' . $db_dir;
    } else {
        $success[] = 'Database directory created ✓';
    }
}

if (is_dir($db_dir) && !is_writable($db_dir)) {
    $errors[] = 'Database directory is not writable: ' . $db_dir;
} else if (is_dir($db_dir)) {
    $success[] = 'Database directory is writable ✓';
}

// Check web server
$server_software = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$success[] = 'Web server: ' . $server_software . ' ✓';

// Check if mod_rewrite is available (for Apache)
if (strpos($server_software, 'Apache') !== false) {
    if (!function_exists('apache_get_modules') || !in_array('mod_rewrite', apache_get_modules())) {
        $warnings[] = 'mod_rewrite may not be available. Clean URLs might not work.';
    } else {
        $success[] = 'mod_rewrite is available ✓';
    }
}

// Check file permissions
$files_to_check = [
    'admin/config.php',
    'api/index.php',
    'setup.php'
];

foreach ($files_to_check as $file) {
    $file_path = __DIR__ . '/' . $file;
    if (!file_exists($file_path)) {
        $errors[] = "Required file missing: $file";
    } else if (!is_readable($file_path)) {
        $errors[] = "File not readable: $file";
    } else {
        $success[] = "File accessible: $file ✓";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BulkAI Licensing System - Installation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status ul {
            margin: 0;
            padding-left: 20px;
        }
        .status h3 {
            margin-top: 0;
        }
        .next-steps {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 20px;
            border-radius: 5px;
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BulkAI Licensing System Installation</h1>

        <?php if (!empty($success)): ?>
        <div class="status success">
            <h3>✓ Requirements Met</h3>
            <ul>
                <?php foreach ($success as $item): ?>
                    <li><?php echo htmlspecialchars($item); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <?php if (!empty($warnings)): ?>
        <div class="status warning">
            <h3>⚠ Warnings</h3>
            <ul>
                <?php foreach ($warnings as $item): ?>
                    <li><?php echo htmlspecialchars($item); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
        <div class="status error">
            <h3>✗ Errors</h3>
            <ul>
                <?php foreach ($errors as $item): ?>
                    <li><?php echo htmlspecialchars($item); ?></li>
                <?php endforeach; ?>
            </ul>
            <p><strong>Please fix these errors before proceeding.</strong></p>
        </div>
        <?php endif; ?>

        <?php if (empty($errors)): ?>
        <div class="next-steps">
            <h3>🎉 Installation Ready!</h3>
            <p>All requirements are met. You can now proceed with the setup.</p>
            
            <h4>Next Steps:</h4>
            <ol>
                <li>Run the setup script to initialize the database</li>
                <li>Change the default admin password</li>
                <li>Configure your desktop application</li>
                <li>Test the API endpoints</li>
            </ol>

            <div style="text-align: center; margin-top: 20px;">
                <a href="setup.php" class="btn btn-success">Run Setup</a>
                <a href="admin/" class="btn">Admin Panel</a>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h4>System Information</h4>
            <ul>
                <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                <li><strong>Web Server:</strong> <?php echo $server_software; ?></li>
                <li><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></li>
                <li><strong>Installation Path:</strong> <?php echo __DIR__; ?></li>
                <li><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
            </ul>
        </div>
        <?php endif; ?>

        <div style="text-align: center; margin-top: 30px; color: #666; font-size: 14px;">
            BulkAI Licensing System v1.0 | 
            <a href="README.md" style="color: #007bff;">Documentation</a>
        </div>
    </div>
</body>
</html>
