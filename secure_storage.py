"""
Secure storage system for API keys using OS-level secure storage mechanisms.

This module provides cross-platform secure storage for sensitive data like API keys,
using the most appropriate storage mechanism for each operating system:
- Windows: Windows Credential Manager
- macOS: Keychain
- Linux: Secret Service API
- Fallback: Encrypted local storage
"""

import os
import sys
import json
import base64
from pathlib import Path
from typing import Optional, Dict, List
from logger import get_logger

# Try to import secure storage libraries
try:
    import keyring
    KEYRING_AVAILABLE = True
except ImportError:
    KEYRING_AVAILABLE = False

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False


class SecureStorage:
    """Secure storage for API keys and sensitive data."""

    def __init__(self, app_name="BulkAI"):
        """Initialize secure storage.

        Args:
            app_name: Application name for keyring service identification
        """
        self.app_name = app_name
        self.service_name = f"{app_name}_API_Keys"
        self.logger = get_logger()

        # Determine storage method
        self.storage_method = self._determine_storage_method()
        self.logger.info(f"Using storage method: {self.storage_method}")

        # Initialize fallback storage if needed
        if self.storage_method == "encrypted_file":
            self._init_encrypted_storage()

    def _determine_storage_method(self) -> str:
        """Determine the best storage method for the current platform."""
        if KEYRING_AVAILABLE:
            try:
                # Test if keyring is working
                test_key = "test_key_bulkai"
                keyring.set_password(self.service_name, test_key, "test_value")
                stored_value = keyring.get_password(self.service_name, test_key)
                if stored_value == "test_value":
                    # Clean up test
                    keyring.delete_password(self.service_name, test_key)
                    return "keyring"
            except Exception as e:
                self.logger.warning(f"Keyring not available: {e}")

        if CRYPTOGRAPHY_AVAILABLE:
            return "encrypted_file"

        self.logger.warning("No secure storage available, falling back to plain text")
        return "plain_file"

    def _init_encrypted_storage(self):
        """Initialize encrypted file storage."""
        self.storage_dir = Path.home() / f".{self.app_name.lower()}"
        self.storage_dir.mkdir(exist_ok=True)
        self.encrypted_file = self.storage_dir / "secure_keys.enc"
        self.key_file = self.storage_dir / ".key"

        # Generate or load encryption key
        if not self.key_file.exists():
            self._generate_encryption_key()

        self.encryption_key = self._load_encryption_key()

    def _generate_encryption_key(self):
        """Generate a new encryption key."""
        # Use machine-specific data for key derivation
        machine_data = f"{os.environ.get('COMPUTERNAME', 'unknown')}{sys.platform}"
        salt = os.urandom(16)

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(machine_data.encode()))

        # Store salt and key
        key_data = {
            "salt": base64.b64encode(salt).decode(),
            "key": key.decode()
        }

        with open(self.key_file, 'w') as f:
            json.dump(key_data, f)

        # Make key file read-only for owner
        if sys.platform != "win32":
            os.chmod(self.key_file, 0o600)

    def _load_encryption_key(self) -> bytes:
        """Load the encryption key."""
        with open(self.key_file, 'r') as f:
            key_data = json.load(f)

        return key_data["key"].encode()

    def store_api_key(self, service: str, api_key: str) -> bool:
        """Store an API key securely.

        Args:
            service: Service name (e.g., 'together_ai', 'runware_ai')
            api_key: API key to store

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.storage_method == "keyring":
                keyring.set_password(self.service_name, service, api_key)
                # Update services list
                services = self.list_stored_services()
                if service not in services:
                    services.append(service)
                    self._update_services_list_keyring(services)
                self.logger.debug(f"API key for {service} stored in keyring")
                return True

            elif self.storage_method == "encrypted_file":
                return self._store_encrypted(service, api_key)

            else:  # plain_file fallback
                return self._store_plain(service, api_key)

        except Exception as e:
            self.logger.error(f"Failed to store API key for {service}: {e}")
            return False

    def get_api_key(self, service: str) -> Optional[str]:
        """Retrieve an API key.

        Args:
            service: Service name

        Returns:
            str: API key if found, None otherwise
        """
        try:
            if self.storage_method == "keyring":
                api_key = keyring.get_password(self.service_name, service)
                if api_key:
                    self.logger.debug(f"API key for {service} retrieved from keyring")
                return api_key

            elif self.storage_method == "encrypted_file":
                return self._get_encrypted(service)

            else:  # plain_file fallback
                return self._get_plain(service)

        except Exception as e:
            self.logger.error(f"Failed to retrieve API key for {service}: {e}")
            return None

    def delete_api_key(self, service: str) -> bool:
        """Delete an API key.

        Args:
            service: Service name

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.storage_method == "keyring":
                keyring.delete_password(self.service_name, service)
                # Update services list
                services = self.list_stored_services()
                if service in services:
                    services.remove(service)
                    self._update_services_list_keyring(services)
                self.logger.debug(f"API key for {service} deleted from keyring")
                return True

            elif self.storage_method == "encrypted_file":
                return self._delete_encrypted(service)

            else:  # plain_file fallback
                return self._delete_plain(service)

        except Exception as e:
            self.logger.error(f"Failed to delete API key for {service}: {e}")
            return False

    def list_stored_services(self) -> List[str]:
        """List all services with stored API keys.

        Returns:
            List[str]: List of service names
        """
        try:
            if self.storage_method == "keyring":
                # Keyring doesn't provide a direct way to list keys
                # We'll maintain a list in a separate entry
                services_list = keyring.get_password(self.service_name, "_services_list")
                if services_list:
                    return json.loads(services_list)
                return []

            elif self.storage_method == "encrypted_file":
                return self._list_encrypted_services()

            else:  # plain_file fallback
                return self._list_plain_services()

        except Exception as e:
            self.logger.error(f"Failed to list stored services: {e}")
            return []

    def _store_encrypted(self, service: str, api_key: str) -> bool:
        """Store API key in encrypted file."""
        # Load existing data
        data = {}
        if self.encrypted_file.exists():
            data = self._load_encrypted_data()

        # Add new key
        data[service] = api_key

        # Encrypt and save
        return self._save_encrypted_data(data)

    def _get_encrypted(self, service: str) -> Optional[str]:
        """Get API key from encrypted file."""
        if not self.encrypted_file.exists():
            return None

        data = self._load_encrypted_data()
        return data.get(service)

    def _delete_encrypted(self, service: str) -> bool:
        """Delete API key from encrypted file."""
        if not self.encrypted_file.exists():
            return True

        data = self._load_encrypted_data()
        if service in data:
            del data[service]
            return self._save_encrypted_data(data)
        return True

    def _list_encrypted_services(self) -> List[str]:
        """List services from encrypted file."""
        if not self.encrypted_file.exists():
            return []

        data = self._load_encrypted_data()
        return list(data.keys())

    def _load_encrypted_data(self) -> Dict[str, str]:
        """Load and decrypt data from file."""
        try:
            with open(self.encrypted_file, 'rb') as f:
                encrypted_data = f.read()

            fernet = Fernet(self.encryption_key)
            decrypted_data = fernet.decrypt(encrypted_data)
            return json.loads(decrypted_data.decode())
        except Exception as e:
            self.logger.error(f"Failed to load encrypted data: {e}")
            return {}

    def _save_encrypted_data(self, data: Dict[str, str]) -> bool:
        """Encrypt and save data to file."""
        try:
            json_data = json.dumps(data).encode()
            fernet = Fernet(self.encryption_key)
            encrypted_data = fernet.encrypt(json_data)

            with open(self.encrypted_file, 'wb') as f:
                f.write(encrypted_data)

            # Make file read-only for owner
            if sys.platform != "win32":
                os.chmod(self.encrypted_file, 0o600)

            return True
        except Exception as e:
            self.logger.error(f"Failed to save encrypted data: {e}")
            return False

    def _store_plain(self, service: str, api_key: str) -> bool:
        """Store API key in plain text file (fallback)."""
        # Ensure storage directory exists
        if not hasattr(self, 'storage_dir'):
            self.storage_dir = Path.home() / f".{self.app_name.lower()}"
            self.storage_dir.mkdir(exist_ok=True)

        plain_file = self.storage_dir / "api_keys.json"

        # Load existing data
        data = {}
        if plain_file.exists():
            try:
                with open(plain_file, 'r') as f:
                    data = json.load(f)
            except:
                pass

        # Add new key
        data[service] = api_key

        # Save
        try:
            with open(plain_file, 'w') as f:
                json.dump(data, f, indent=2)

            # Make file read-only for owner
            if sys.platform != "win32":
                os.chmod(plain_file, 0o600)

            return True
        except Exception as e:
            self.logger.error(f"Failed to save plain data: {e}")
            return False

    def _get_plain(self, service: str) -> Optional[str]:
        """Get API key from plain text file."""
        # Ensure storage directory exists
        if not hasattr(self, 'storage_dir'):
            self.storage_dir = Path.home() / f".{self.app_name.lower()}"
            self.storage_dir.mkdir(exist_ok=True)

        plain_file = self.storage_dir / "api_keys.json"
        if not plain_file.exists():
            return None

        try:
            with open(plain_file, 'r') as f:
                data = json.load(f)
            return data.get(service)
        except:
            return None

    def _delete_plain(self, service: str) -> bool:
        """Delete API key from plain text file."""
        # Ensure storage directory exists
        if not hasattr(self, 'storage_dir'):
            self.storage_dir = Path.home() / f".{self.app_name.lower()}"
            self.storage_dir.mkdir(exist_ok=True)

        plain_file = self.storage_dir / "api_keys.json"
        if not plain_file.exists():
            return True

        try:
            with open(plain_file, 'r') as f:
                data = json.load(f)

            if service in data:
                del data[service]

                with open(plain_file, 'w') as f:
                    json.dump(data, f, indent=2)

            return True
        except Exception as e:
            self.logger.error(f"Failed to delete from plain file: {e}")
            return False

    def _list_plain_services(self) -> List[str]:
        """List services from plain text file."""
        # Ensure storage directory exists
        if not hasattr(self, 'storage_dir'):
            self.storage_dir = Path.home() / f".{self.app_name.lower()}"
            self.storage_dir.mkdir(exist_ok=True)

        plain_file = self.storage_dir / "api_keys.json"
        if not plain_file.exists():
            return []

        try:
            with open(plain_file, 'r') as f:
                data = json.load(f)
            return list(data.keys())
        except:
            return []

    def _update_services_list_keyring(self, services: List[str]):
        """Update the services list in keyring."""
        try:
            keyring.set_password(self.service_name, "_services_list", json.dumps(services))
        except Exception as e:
            self.logger.error(f"Failed to update services list: {e}")
