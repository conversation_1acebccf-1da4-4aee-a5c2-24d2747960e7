#!/usr/bin/env python3
"""
BulkAI Desktop Application Build Script (Simple Version)

This script automates the process of building a standalone executable (.exe) 
from the BulkAI desktop application using PyInstaller.

This version uses simple text output to avoid Unicode encoding issues on Windows.
"""

import os
import sys
import shutil
import subprocess
import time
import io
from pathlib import Path


class BuildError(Exception):
    """Custom exception for build-related errors."""
    pass


class BulkAIBuilder:
    """Main builder class for BulkAI desktop application."""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.spec_file = self.project_root / "bulky.spec"
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.executable_name = "Pixeliano - Unlimited AI Images. One Click Away.exe"
        
    def check_prerequisites(self):
        """Check if all prerequisites are met."""
        print("[CHECK] Checking prerequisites...")
        
        # Check if PyInstaller is installed
        try:
            result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                                  capture_output=True, text=True, check=True)
            print(f"[OK] PyInstaller version: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise BuildError("[ERROR] PyInstaller is not installed. Install it with: pip install pyinstaller")
        
        # Check if spec file exists
        if not self.spec_file.exists():
            raise BuildError(f"[ERROR] Spec file not found: {self.spec_file}")
        print(f"[OK] Spec file found: {self.spec_file}")
        
        # Check if main.py exists
        main_py = self.project_root / "main.py"
        if not main_py.exists():
            raise BuildError(f"[ERROR] Main script not found: {main_py}")
        print(f"[OK] Main script found: {main_py}")
        
        # Check if required dependencies are installed
        required_modules = [
            "PyQt6",
            "PIL",
            "requests",
            "keyring",
            "cryptography",
            "runware"
        ]

        for module in required_modules:
            try:
                __import__(module)
                print(f"[OK] Module {module} is available")
            except ImportError:
                print(f"[WARNING] Module {module} not found - install with: pip install {module}")

        # Check if required resource files exist
        required_files = [
            "config.json",
            "test_prompts.txt",
            "ui/resources/app-logo.svg",
            "ui/resources/splash-screen.svg",
            "requirements.txt"
        ]

        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                print(f"[WARNING] Required file not found: {file_path}")
            else:
                print(f"[OK] Resource file found: {file_path}")

        # Check if application modules exist
        app_modules = [
            "config_manager.py",
            "app_controller.py",
            "license_manager.py",
            "usage_tracker.py",
            "secure_storage.py",
            "logger.py",
            "ui/main_window.py",
            "ui/splash_screen.py",
            "ui/styles.py",
            "ui/api_key_manager.py",
            "api/api_client.py",
            "api/runware_client.py",
            "api/replicate_client.py"
        ]

        for module_path in app_modules:
            full_path = self.project_root / module_path
            if not full_path.exists():
                print(f"[WARNING] Application module not found: {module_path}")
            else:
                print(f"[OK] Application module found: {module_path}")
    
    def backup_spec_file(self):
        """Create a backup of the original spec file."""
        backup_path = self.spec_file.with_suffix('.spec.backup')
        if not backup_path.exists():
            shutil.copy2(self.spec_file, backup_path)
            print(f"[INFO] Backup created: {backup_path}")
        else:
            print(f"[INFO] Backup already exists: {backup_path}")
    
    def update_spec_file(self):
        """Update the spec file for standalone distribution."""
        print("[CONFIG] Updating spec file for standalone distribution...")
        
        # Read the current spec file
        with open(self.spec_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if it's already configured for onefile
        if 'exclude_binaries=False' in content and 'a.binaries,' in content:
            print("[OK] Spec file already configured for standalone distribution")
            return
        
        # Create the updated spec file content for onefile distribution
        updated_content = '''# -*- mode: python ; coding: utf-8 -*-
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all submodules for comprehensive inclusion
ui_modules = collect_submodules('ui')
api_modules = collect_submodules('api')

# Add data files - include all necessary configuration and resource files
datas = [
    ('config.json', '.'),
    ('test_prompts.txt', '.'),
    ('ui/resources/app-logo.svg', 'ui/resources'),
    ('ui/resources/splash-screen.svg', 'ui/resources'),
    ('ui/resources/splash-screen-backup.svg', 'ui/resources'),
    ('ui/resources/README.md', 'ui/resources'),
    ('usage_data.json', '.') if os.path.exists('usage_data.json') else None,
    ('requirements.txt', '.'),
]

# Remove None entries
datas = [d for d in datas if d is not None]

# Create generated_images directory if it doesn't exist
if not os.path.exists('generated_images'):
    os.makedirs('generated_images')

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # PyQt6 modules
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtSvg',

        # Image processing
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageDraw',
        'PIL.ImageFont',

        # HTTP and API clients
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.exceptions',
        'requests.models',
        'requests.sessions',
        'requests.utils',
        'runware',

        # Security and storage
        'keyring',
        'keyring.backends',
        'keyring.backends.Windows',
        'keyring.backends.macOS',
        'keyring.backends.SecretService',
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.primitives.hashes',
        'cryptography.hazmat.primitives.kdf',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'wincred',

        # Standard library modules that might not be auto-detected
        'asyncio',
        'uuid',
        'base64',
        'json',
        'threading',
        'subprocess',
        'pathlib',
        'datetime',
        'platform',
        'hashlib',
        'typing',
        'signal',
        'traceback',
        'io',
        'sys',
        'os',
        'time',
        'shutil',

        # Application modules
        'config_manager',
        'app_controller',
        'license_manager',
        'usage_tracker',
        'secure_storage',
        'logger',

        # UI modules
        'ui',
        'ui.main_window',
        'ui.splash_screen',
        'ui.styles',
        'ui.api_key_manager',

        # API modules
        'api',
        'api.api_client',
        'api.runware_client',
        'api.replicate_client',
    ] + ui_modules + api_modules,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Pixeliano - Unlimited AI Images. One Click Away',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
        
        # Write the updated content
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("[OK] Spec file updated for standalone distribution")
    
    def clean_previous_builds(self):
        """Clean up previous build artifacts."""
        print("[CLEAN] Cleaning previous build artifacts...")
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            print(f"[DELETE] Removed: {self.dist_dir}")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"[DELETE] Removed: {self.build_dir}")
    
    def run_pyinstaller(self):
        """Run PyInstaller to build the executable."""
        print("[BUILD] Building executable with PyInstaller...")
        print("This may take several minutes...")
        
        start_time = time.time()
        
        try:
            # Run PyInstaller with the spec file
            cmd = [sys.executable, "-m", "PyInstaller", "--clean", str(self.spec_file)]
            
            print(f"Running command: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            
            build_time = time.time() - start_time
            print(f"[OK] Build completed successfully in {build_time:.1f} seconds")
                
        except subprocess.CalledProcessError as e:
            print(f"[ERROR] Build failed with exit code {e.returncode}")
            print(f"Error output: {e.stderr}")
            if e.stdout:
                print(f"Standard output: {e.stdout}")
            raise BuildError(f"PyInstaller build failed: {e}")
    
    def verify_output(self):
        """Verify that the executable was created successfully."""
        print("[CHECK] Verifying build output...")
        
        executable_path = self.dist_dir / self.executable_name
        
        if not executable_path.exists():
            raise BuildError(f"[ERROR] Executable not found: {executable_path}")
        
        file_size = executable_path.stat().st_size
        file_size_mb = file_size / (1024 * 1024)
        
        print(f"[OK] Executable created: {executable_path}")
        print(f"[STATS] File size: {file_size_mb:.1f} MB")
        
        return executable_path
    
    def cleanup_build_files(self, keep_dist=True):
        """Clean up temporary build files."""
        print("[CLEAN] Cleaning up temporary build files...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"[DELETE] Removed build directory: {self.build_dir}")
        
        # Remove __pycache__ directories
        for pycache_dir in self.project_root.rglob("__pycache__"):
            if pycache_dir.is_dir():
                shutil.rmtree(pycache_dir)
                print(f"[DELETE] Removed: {pycache_dir}")
        
        if not keep_dist:
            if self.dist_dir.exists():
                shutil.rmtree(self.dist_dir)
                print(f"[DELETE] Removed dist directory: {self.dist_dir}")
    
    def build(self, clean_after=False):
        """Main build method that orchestrates the entire build process."""
        try:
            print("[START] Starting BulkAI Desktop Application build process...")
            print("=" * 60)
            
            # Step 1: Check prerequisites
            self.check_prerequisites()
            
            # Step 2: Backup original spec file
            self.backup_spec_file()
            
            # Step 3: Update spec file for standalone distribution
            self.update_spec_file()
            
            # Step 4: Clean previous builds
            self.clean_previous_builds()
            
            # Step 5: Run PyInstaller
            self.run_pyinstaller()
            
            # Step 6: Verify output
            executable_path = self.verify_output()
            
            # Step 7: Optional cleanup
            if clean_after:
                self.cleanup_build_files(keep_dist=True)
            
            print("=" * 60)
            print("[SUCCESS] Build completed successfully!")
            print(f"[PACKAGE] Executable location: {executable_path}")
            print("[TIP] You can now distribute this standalone executable to end users.")
            
            return executable_path
            
        except BuildError as e:
            print(f"\n[ERROR] Build failed: {e}")
            return None
        except Exception as e:
            print(f"\n[CRASH] Unexpected error: {e}")
            return None


def main():
    """Main entry point for the build script."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Build BulkAI Desktop Application executable")
    parser.add_argument("--clean", action="store_true", 
                       help="Clean up temporary build files after successful build")
    parser.add_argument("--no-backup", action="store_true",
                       help="Skip creating backup of spec file")
    
    args = parser.parse_args()
    
    builder = BulkAIBuilder()
    
    if not args.no_backup:
        builder.backup_spec_file()
    
    result = builder.build(clean_after=args.clean)
    
    if result:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
