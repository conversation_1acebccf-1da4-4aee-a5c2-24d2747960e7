<?php
require_once 'config.php';
checkAuth();

$pdo = getDatabase();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                try {
                    $stmt = $pdo->prepare("INSERT INTO customers (name, email, company, phone) VALUES (?, ?, ?, ?)");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['email'],
                        $_POST['company'],
                        $_POST['phone']
                    ]);
                    $message = 'Customer created successfully!';
                } catch (PDOException $e) {
                    $error = 'Error creating customer: ' . $e->getMessage();
                }
                break;

            case 'update':
                try {
                    $stmt = $pdo->prepare("UPDATE customers SET name = ?, email = ?, company = ?, phone = ?, status = ?, updated_date = CURRENT_TIMESTAMP WHERE id = ?");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['email'],
                        $_POST['company'],
                        $_POST['phone'],
                        $_POST['status'],
                        $_POST['id']
                    ]);
                    $message = 'Customer updated successfully!';
                } catch (PDOException $e) {
                    $error = 'Error updating customer: ' . $e->getMessage();
                }
                break;

            case 'delete':
                try {
                    $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'Customer deleted successfully!';
                } catch (PDOException $e) {
                    $error = 'Error deleting customer: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get customer for editing
$edit_customer = null;
if (isset($_GET['edit'])) {
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_customer = $stmt->fetch();
}

// Get all customers
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$sql = "SELECT * FROM customers WHERE 1=1";
$params = [];

if ($search) {
    $sql .= " AND (name LIKE ? OR email LIKE ? OR company LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $sql .= " AND status = ?";
    $params[] = $status_filter;
}

$sql .= " ORDER BY created_date DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$customers = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customers - BulkAI License Admin</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="dashboard">
    <div class="header">
        <h1>BulkAI License Admin</h1>
        <nav class="nav">
            <a href="dashboard.php">Dashboard</a>
            <a href="customers.php" class="active">Customers</a>
            <a href="licenses.php">Licenses</a>
            <a href="usage.php">Usage</a>
            <a href="index.php?logout=1">Logout</a>
        </nav>
    </div>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <!-- Customer Form -->
        <div class="table-container">
            <div class="table-header">
                <h2><?php echo $edit_customer ? 'Edit Customer' : 'Add New Customer'; ?></h2>
            </div>
            <div style="padding: 20px;">
                <form method="POST">
                    <input type="hidden" name="action" value="<?php echo $edit_customer ? 'update' : 'create'; ?>">
                    <?php if ($edit_customer): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_customer['id']; ?>">
                    <?php endif; ?>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label for="name">Name *</label>
                            <input type="text" id="name" name="name" required
                                   value="<?php echo htmlspecialchars($edit_customer['name'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" required
                                   value="<?php echo htmlspecialchars($edit_customer['email'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="company">Company</label>
                            <input type="text" id="company" name="company"
                                   value="<?php echo htmlspecialchars($edit_customer['company'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="phone">Phone</label>
                            <input type="text" id="phone" name="phone"
                                   value="<?php echo htmlspecialchars($edit_customer['phone'] ?? ''); ?>">
                        </div>

                        <?php if ($edit_customer): ?>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status">
                                <option value="active" <?php echo ($edit_customer['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo ($edit_customer['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                <option value="suspended" <?php echo ($edit_customer['status'] === 'suspended') ? 'selected' : ''; ?>>Suspended</option>
                            </select>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div style="margin-top: 20px;">
                        <button type="submit" class="btn btn-primary">
                            <?php echo $edit_customer ? 'Update Customer' : 'Create Customer'; ?>
                        </button>
                        <?php if ($edit_customer): ?>
                            <a href="customers.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="table-container">
            <div class="table-header">
                <h2>All Customers</h2>
            </div>
            <div style="padding: 20px; border-bottom: 1px solid #dee2e6;">
                <form method="GET" style="display: flex; gap: 15px; align-items: end;">
                    <div class="form-group" style="margin-bottom: 0; flex: 1;">
                        <label for="search">Search</label>
                        <input type="text" id="search" name="search" placeholder="Name, email, or company..."
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="active" <?php echo ($status_filter === 'active') ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($status_filter === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                            <option value="suspended" <?php echo ($status_filter === 'suspended') ? 'selected' : ''; ?>>Suspended</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Search</button>
                    <a href="customers.php" class="btn btn-secondary">Clear</a>
                </form>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Company</th>
                        <th>Phone</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($customers as $customer): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($customer['name']); ?></td>
                        <td><?php echo htmlspecialchars($customer['email']); ?></td>
                        <td><?php echo htmlspecialchars($customer['company'] ?? 'N/A'); ?></td>
                        <td><?php echo htmlspecialchars($customer['phone'] ?? 'N/A'); ?></td>
                        <td><?php echo getStatusBadge($customer['status']); ?></td>
                        <td><?php echo formatDate($customer['created_date']); ?></td>
                        <td>
                            <a href="customers.php?edit=<?php echo $customer['id']; ?>" class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">Edit</a>
                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this customer?');">
                                <input type="hidden" name="action" value="delete">
                                <input type="hidden" name="id" value="<?php echo $customer['id']; ?>">
                                <button type="submit" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Delete</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>

                    <?php if (empty($customers)): ?>
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                            No customers found.
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
