# BulkAI Image Generator

A modern, professional desktop application for generating AI images using the Together AI FLUX.1-schnell-Free model.

## Features

- Modern, sleek UI with both dark and light themes
- Real-time image generation as you type
- Customizable image size, generation steps, and seed
- Save generated images to your computer
- API key management through config file
- Responsive and user-friendly interface
- Professional-grade UI components

## Requirements

- Python 3.8 or higher
- PyQt6
- Pillow
- Requests

## Installation

1. Clone this repository or download the source code.

2. Install the required dependencies:

```bash
pip install PyQt6 Pillow requests
```

3. Set up your Together AI API key:
   - Sign up at [Together AI](https://www.together.ai/)
   - Get your API key from your account dashboard
   - Enter it when prompted by the application or add it to the config.json file

## Usage

1. Run the application:

```bash
python main.py
```

2. Enter your API key when prompted (if not already configured).

3. Enter a prompt describing the image you want to generate.

4. Adjust settings as needed:
   - Image Size: Select from predefined size options
   - Steps: Higher values produce better quality but take longer (1-10)
   - Seed: Set a specific seed for reproducible results or leave as "Random"
   - Auto-generate: Toggle automatic generation as you type

5. Click "Generate Image" or wait for the automatic generation to complete.

6. Save the generated image using the "Save Image" button.

## Application Structure

- `main.py` - Main entry point
- `app_controller.py` - Application controller connecting UI and API
- `config_manager.py` - Configuration management
- `api/` - API client for Together AI
- `ui/` - User interface components
  - `main_window.py` - Main application window
  - `styles.py` - UI styles and themes
  - `resources/` - Icons and images

## Configuration

The application stores configuration in a JSON file located at:
- Windows: `C:\Users\<USER>\.bulkai\config.json`
- macOS/Linux: `~/.bulkai/config.json`

This file stores:
- API keys
- UI theme preference
- Default settings
- Runware AI model configurations

### Configuring Runware AI Models

You can define custom Runware AI models in the `config.json` file under the `runware_models` section:

```json
"runware_models": [
    {
        "id": "runware:100@1",
        "name": "FLUX Schnell"
    },
    {
        "id": "civitai:4201@130072",
        "name": "Realistic Vision V6.0 B1"
    }
]
```

Each model requires:
- `id`: The model identifier in Runware's AIR format (e.g., `runware:100@1`, `civitai:4201@130072`)
- `name`: A user-friendly name to display in the UI

You can add as many models as you want, and they will be dynamically loaded into the application.

## Tips for Better Results

- Be specific and detailed in your prompts
- Experiment with different step values to balance quality and speed
- Use a consistent seed value when iterating on a specific concept

## License

MIT

## Acknowledgements

- [Together AI](https://www.together.ai/) for providing the FLUX.1-schnell-Free model
- [PyQt6](https://www.riverbankcomputing.com/software/pyqt/) for the UI framework
- [Pillow](https://python-pillow.org/) for image processing
