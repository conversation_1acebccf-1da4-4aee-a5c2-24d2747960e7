#!/usr/bin/env python3
"""
Test script for license activation endpoint.
This script helps debug the 404 error issue.
"""

import requests
import json
import platform
import hashlib

def test_license_activation():
    """Test the license activation endpoint."""

    # Configuration
    license_server_url = "https://bulkimages.azanx.com/licensing/api"
    test_license_key = "BULKAI-67BD2EA2-CE1423CF-40039D04"  # Replace with actual test license key

    # Generate a test device ID
    hardware_string = f"{platform.machine()}-{platform.processor()}-{platform.system()}-{platform.node()}"
    device_id = hashlib.sha256(hardware_string.encode()).hexdigest()[:16]

    print(f"Testing license activation endpoint...")
    print(f"Server URL: {license_server_url}")
    print(f"Device ID: {device_id}")
    print(f"License Key: {test_license_key}")
    print("-" * 50)

    # Test different URL formats (based on test results, the query parameter format works)
    test_urls = [
        f"{license_server_url}/?action=activate-license",  # This format works!
        f"{license_server_url}/activate-license",          # This returns 404
        f"{license_server_url}/index.php/activate-license" # This returns 404
    ]

    device_info = {
        'system': platform.system(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': platform.python_version()
    }

    payload = {
        'license_key': test_license_key,
        'device_id': device_id,
        'device_name': platform.node(),
        'device_info': json.dumps(device_info)
    }

    for url in test_urls:
        print(f"\nTesting URL: {url}")
        try:
            response = requests.post(
                url,
                json=payload,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )

            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")

            try:
                response_data = response.json()
                print(f"Response JSON: {json.dumps(response_data, indent=2)}")
            except:
                print(f"Response Text: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"Request Error: {e}")

        print("-" * 30)

if __name__ == "__main__":
    test_license_activation()
