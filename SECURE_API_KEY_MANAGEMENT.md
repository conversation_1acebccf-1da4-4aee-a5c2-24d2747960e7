# Secure API Key Management System

## Overview

The BulkAI desktop application now implements a comprehensive secure API key management system that prevents API keys from being bundled with the executable and provides users with a secure way to manage their own API keys.

## Key Features

### 🔒 **Secure Storage**
- **Windows**: Uses Windows Credential Manager
- **macOS**: Uses Keychain
- **Linux**: Uses Secret Service API
- **Fallback**: Encrypted local storage using industry-standard encryption

### 🛡️ **Security Benefits**
- API keys are never stored in plain text config files
- Keys are not bundled with the application executable
- Each user manages their own API keys securely
- Automatic migration from legacy config.json format

### 🎯 **User-Friendly Management**
- Comprehensive API Key Manager dialog
- Add, edit, delete, and test API keys
- Visual status indicators for configured services
- Integration with trial/license restrictions

## Architecture

### Core Components

1. **`secure_storage.py`** - Cross-platform secure storage implementation
2. **`ui/api_key_manager.py`** - Comprehensive API key management UI
3. **`config_manager.py`** - Updated to use secure storage instead of JSON
4. **`ui/main_window.py`** - Integrated with new API key management

### Storage Methods (Priority Order)

1. **Keyring** (Preferred) - Uses OS-level secure storage
2. **Encrypted File** - AES encryption with machine-specific key derivation
3. **Plain File** (Fallback) - File permissions restricted to owner only

## Usage

### For Users

#### First-Time Setup
1. Launch the application
2. When prompted for API keys, click "Yes" to open the API Key Manager
3. Add your API keys for the services you want to use
4. Keys are automatically stored securely

#### Managing API Keys
1. Go to **Settings > Manage API Keys**
2. Use the comprehensive manager to:
   - Add new API keys
   - Edit existing keys
   - Delete unwanted keys
   - Test key validity
   - View configured services

#### Supported Services
- **Together AI** (Required) - For FLUX models
- **Runware AI** (Optional) - For various AI models (Pro license required)

### For Developers

#### Key Classes

```python
# Secure storage
from secure_storage import SecureStorage
storage = SecureStorage("BulkAI")
storage.store_api_key("together_ai", "your_api_key")
api_key = storage.get_api_key("together_ai")

# Config manager (automatically uses secure storage)
from config_manager import ConfigManager
config = ConfigManager()
config.set_api_key("your_api_key", "together_ai")
api_key = config.get_api_key("together_ai")

# API key manager UI
from ui.api_key_manager import ApiKeyManagerDialog
dialog = ApiKeyManagerDialog(parent, secure_storage, license_manager)
dialog.exec()
```

## Migration Process

### Automatic Legacy Migration
When the application starts with existing API keys in `config.json`:

1. **Detection**: Config manager detects legacy API keys
2. **Migration**: Keys are automatically moved to secure storage
3. **Cleanup**: API keys are removed from `config.json`
4. **Verification**: Migration success is logged

### Manual Migration
If automatic migration fails, users can:

1. Note their existing API keys from the old config
2. Use the API Key Manager to re-enter them
3. Delete the old config file if needed

## Security Considerations

### Encryption Details
- **Algorithm**: AES-256 in Fernet mode (cryptography library)
- **Key Derivation**: PBKDF2-HMAC-SHA256 with 100,000 iterations
- **Salt**: Random 16-byte salt per installation
- **Machine Binding**: Key derivation includes machine-specific data

### File Permissions
- Encrypted files: Owner read/write only (0600 on Unix)
- Key files: Owner read/write only (0600 on Unix)
- Windows: Uses NTFS permissions

### Threat Model
**Protected Against:**
- Accidental exposure in config files
- API keys bundled with executables
- Casual file system browsing
- Basic malware scanning config files

**Not Protected Against:**
- Malware with admin/root privileges
- Physical access to unlocked machine
- Memory dumps of running process
- Sophisticated targeted attacks

## Testing

### Running Tests
```bash
python test_secure_api_keys.py
```

### Test Coverage
- Secure storage functionality
- API key CRUD operations
- Legacy migration
- Cross-platform compatibility
- Error handling

## Troubleshooting

### Common Issues

#### "No secure storage available"
- **Cause**: Missing dependencies or system limitations
- **Solution**: Install required packages: `pip install keyring cryptography`

#### "Failed to store API key"
- **Cause**: Permission issues or corrupted storage
- **Solution**: Check file permissions, restart application

#### "API key not found after restart"
- **Cause**: Storage method changed or corruption
- **Solution**: Re-enter API keys using the manager

### Debug Information
Enable debug logging to see storage method selection:
```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Dependencies

### Required
- `keyring>=24.0.0` - Cross-platform secure storage
- `cryptography>=41.0.0` - Encryption fallback

### Platform-Specific
- `wincred>=1.0.2` - Windows Credential Manager (Windows only)

## Future Enhancements

### Planned Features
- [ ] API key expiration tracking
- [ ] Multiple API keys per service
- [ ] Import/export functionality (encrypted)
- [ ] Centralized key management for organizations
- [ ] Hardware security module (HSM) support

### Security Improvements
- [ ] Additional encryption layers
- [ ] Biometric authentication integration
- [ ] Audit logging for key access
- [ ] Key rotation automation

## Contributing

When contributing to the secure API key management system:

1. **Security First**: Always consider security implications
2. **Cross-Platform**: Test on Windows, macOS, and Linux
3. **Backward Compatibility**: Ensure migration paths work
4. **User Experience**: Keep the UI simple and intuitive
5. **Documentation**: Update this document with changes

## License Integration

The secure API key management system respects the existing license restrictions:

- **Trial Users**: Can only configure Together AI keys
- **Pro Users**: Can configure all supported service keys
- **UI Restrictions**: API Key Manager adapts to license level

## Support

For issues related to secure API key management:

1. Check the troubleshooting section above
2. Run the test script to verify functionality
3. Check application logs for detailed error messages
4. Report issues with system information and log excerpts
