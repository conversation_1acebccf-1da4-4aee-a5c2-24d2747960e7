-- BulkAI Licensing System Database Schema

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    company TEXT,
    phone TEXT,
    created_date D<PERSON><PERSON><PERSON>E DEFAULT CURRENT_TIMESTAMP,
    updated_date D<PERSON>ETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended'))
);

-- License plans table
CREATE TABLE IF NOT EXISTS license_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plan_name TEXT UNIQUE NOT NULL,
    plan_type TEXT NOT NULL CHECK (plan_type IN ('free', 'monthly', 'yearly', 'lifetime')),
    price DECIMAL(10,2) DEFAULT 0.00,
    daily_image_limit INTEGER DEFAULT -1, -- -1 means unlimited
    features TEXT, -- JSON string of features
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Licenses table
CREATE TABLE IF NOT EXISTS licenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT UNIQUE NOT NULL,
    customer_id INTEGER NOT NULL,
    plan_id INTEGER NOT NULL,
    activation_date DATETIME,
    expiry_date DATETIME,
    max_activations INTEGER DEFAULT 1,
    current_activations INTEGER DEFAULT 0,
    status TEXT DEFAULT 'inactive' CHECK (status IN ('active', 'inactive', 'expired', 'suspended', 'revoked')),
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES license_plans (id) ON DELETE RESTRICT
);

-- Device activations table
CREATE TABLE IF NOT EXISTS device_activations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_id INTEGER NOT NULL,
    device_id TEXT NOT NULL,
    device_name TEXT,
    device_info TEXT, -- JSON string with device details
    activation_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    deactivation_date DATETIME,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deactivated')),
    FOREIGN KEY (license_id) REFERENCES licenses (id) ON DELETE CASCADE,
    UNIQUE(license_id, device_id)
);

-- Usage tracking table
CREATE TABLE IF NOT EXISTS usage_tracking (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_id INTEGER NOT NULL,
    device_id TEXT NOT NULL,
    usage_date DATE NOT NULL,
    images_generated INTEGER DEFAULT 0,
    api_calls INTEGER DEFAULT 0,
    provider_used TEXT,
    model_used TEXT,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (license_id) REFERENCES licenses (id) ON DELETE CASCADE,
    UNIQUE(license_id, device_id, usage_date)
);

-- API access logs table
CREATE TABLE IF NOT EXISTS api_access_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT,
    device_id TEXT,
    endpoint TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    request_data TEXT,
    response_status INTEGER,
    access_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    processing_time_ms INTEGER
);

-- Insert default license plans
INSERT OR IGNORE INTO license_plans (plan_name, plan_type, price, daily_image_limit, features, description) VALUES
('Free Plan', 'free', 0.00, 10, '{"models": ["flux_schnell"], "providers": ["together_ai"], "support": "community"}', 'Free plan with 10 images per day, Flux Schnell model only'),
('Monthly Pro', 'monthly', 29.99, -1, '{"models": ["all"], "providers": ["all"], "support": "email", "priority": "standard"}', 'Monthly subscription with unlimited images and all models'),
('Yearly Pro', 'yearly', 299.99, -1, '{"models": ["all"], "providers": ["all"], "support": "email", "priority": "high"}', 'Yearly subscription with unlimited images and all models'),
('Lifetime Pro', 'lifetime', 999.99, -1, '{"models": ["all"], "providers": ["all"], "support": "priority", "priority": "highest"}', 'One-time purchase with lifetime access to all features');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_licenses_license_key ON licenses(license_key);
CREATE INDEX IF NOT EXISTS idx_licenses_customer_id ON licenses(customer_id);
CREATE INDEX IF NOT EXISTS idx_licenses_status ON licenses(status);
CREATE INDEX IF NOT EXISTS idx_device_activations_license_id ON device_activations(license_id);
CREATE INDEX IF NOT EXISTS idx_device_activations_device_id ON device_activations(device_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_license_date ON usage_tracking(license_id, usage_date);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_api_logs_license_key ON api_access_logs(license_key);
CREATE INDEX IF NOT EXISTS idx_api_logs_access_time ON api_access_logs(access_time);
