"""
API Key Management UI for BulkAI Desktop Application.

This module provides a comprehensive interface for managing API keys securely,
including adding, editing, deleting, and testing API keys for different services.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QComboBox,
    QGroupBox, QTextEdit, QProgressBar, QCheckBox, QWidget
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QIcon, QFont
from typing import Optional, Dict, List
from logger import get_logger


class ApiKeyTestWorker(QThread):
    """Worker thread for testing API keys."""

    test_completed = pyqtSignal(str, bool, str)  # service, success, message

    def __init__(self, service: str, api_key: str):
        super().__init__()
        self.service = service
        self.api_key = api_key
        self.logger = get_logger()

    def run(self):
        """Test the API key."""
        try:
            if self.service == "together_ai":
                success, message = self._test_together_ai()
            elif self.service == "runware_ai":
                success, message = self._test_runware_ai()
            elif self.service == "replicate_ai":
                success, message = self._test_replicate_ai()
            else:
                success, message = False, f"Unknown service: {self.service}"

            self.test_completed.emit(self.service, success, message)
        except Exception as e:
            self.logger.error(f"Error testing API key for {self.service}: {e}")
            self.test_completed.emit(self.service, False, f"Test failed: {str(e)}")

    def _test_together_ai(self) -> tuple[bool, str]:
        """Test Together AI API key."""
        try:
            import requests

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # Simple API call to test the key
            response = requests.get(
                "https://api.together.xyz/v1/models",
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                return True, "API key is valid"
            elif response.status_code == 401:
                return False, "Invalid API key"
            else:
                return False, f"API returned status {response.status_code}"

        except requests.exceptions.Timeout:
            return False, "Request timed out"
        except requests.exceptions.RequestException as e:
            return False, f"Network error: {str(e)}"
        except Exception as e:
            return False, f"Test failed: {str(e)}"

    def _test_runware_ai(self) -> tuple[bool, str]:
        """Test Runware AI API key."""
        try:
            import requests

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # Test with a simple request
            data = {
                "taskType": "imageInference",
                "taskUUID": "test-uuid",
                "model": "runware:100@1",
                "positivePrompt": "test",
                "width": 512,
                "height": 512,
                "steps": 1,
                "CFGScale": 1
            }

            response = requests.post(
                "https://api.runware.ai/v1",
                headers=headers,
                json=data,
                timeout=10
            )

            if response.status_code in [200, 202]:
                return True, "API key is valid"
            elif response.status_code == 401:
                return False, "Invalid API key"
            else:
                return False, f"API returned status {response.status_code}"

        except requests.exceptions.Timeout:
            return False, "Request timed out"
        except requests.exceptions.RequestException as e:
            return False, f"Network error: {str(e)}"
        except Exception as e:
            return False, f"Test failed: {str(e)}"

    def _test_replicate_ai(self) -> tuple[bool, str]:
        """Test Replicate AI API key."""
        try:
            import requests

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # Test with a simple request to list models
            response = requests.get(
                "https://api.replicate.com/v1/models",
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                return True, "API key is valid"
            elif response.status_code == 401:
                return False, "Invalid API key"
            else:
                return False, f"API returned status {response.status_code}"

        except requests.exceptions.Timeout:
            return False, "Request timed out"
        except requests.exceptions.RequestException as e:
            return False, f"Network error: {str(e)}"
        except Exception as e:
            return False, f"Test failed: {str(e)}"


class ApiKeyManagerDialog(QDialog):
    """Comprehensive API Key Management Dialog."""

    def __init__(self, parent=None, secure_storage=None, license_manager=None):
        super().__init__(parent)
        self.secure_storage = secure_storage
        self.license_manager = license_manager
        self.logger = get_logger()

        self.setWindowTitle("API Key Manager")
        self.setMinimumSize(800, 600)
        self.resize(900, 700)
        self.setModal(True)

        # Available services (filtered based on license)
        self.all_services = {
            "together_ai": {
                "name": "Together AI",
                "description": "Together AI API for FLUX models",
                "required": True,
                "trial_allowed": True
            },
            "runware_ai": {
                "name": "Runware AI",
                "description": "Runware AI API for various models (Pro license required)",
                "required": False,
                "trial_allowed": False
            },
            "replicate_ai": {
                "name": "Replicate AI",
                "description": "Replicate AI API for FLUX Dev and FLUX Schnell models (Pro license required)",
                "required": False,
                "trial_allowed": False
            }
        }

        # Filter services based on license
        self.services = self._filter_services_by_license()

        self.setup_ui()
        self.load_existing_keys()

    def _filter_services_by_license(self):
        """Filter available services based on license plan.

        Returns:
            dict: Filtered services dictionary based on license restrictions
        """
        if not self.license_manager:
            # If no license manager, allow all services (fallback)
            return self.all_services.copy()

        filtered_services = {}
        is_trial = self.license_manager.is_trial_plan()

        for service_id, service_info in self.all_services.items():
            if is_trial and not service_info.get("trial_allowed", True):
                # Skip services not allowed for trial users
                continue
            filtered_services[service_id] = service_info.copy()

        return filtered_services

    def _is_service_restricted(self, service_id):
        """Check if a service is restricted for the current license.

        Args:
            service_id (str): Service identifier

        Returns:
            bool: True if service is restricted, False otherwise
        """
        if not self.license_manager:
            return False

        service_info = self.all_services.get(service_id, {})
        is_trial = self.license_manager.is_trial_plan()

        return is_trial and not service_info.get("trial_allowed", True)

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Title
        title_label = QLabel("API Key Manager")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Description
        desc_text = "Manage your API keys securely. Keys are stored using your system's secure storage."

        # Add license restriction info for trial users
        if self.license_manager and self.license_manager.is_trial_plan():
            desc_text += "\n\n⚠️ Trial Plan: Only Together AI is available. Upgrade to Pro to access Runware AI and Replicate AI."

        desc_label = QLabel(desc_text)
        desc_label.setWordWrap(True)
        if self.license_manager and self.license_manager.is_trial_plan():
            desc_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(255, 193, 7, 0.1);
                    border: 1px solid rgba(255, 193, 7, 0.3);
                    border-radius: 4px;
                    padding: 8px;
                    color: #856404;
                }
            """)
        layout.addWidget(desc_label)

        # API Keys Table
        self.setup_keys_table()
        layout.addWidget(self.keys_table)

        # Add/Edit Key Section
        self.setup_add_edit_section()
        layout.addWidget(self.add_edit_group)

        # Buttons
        self.setup_buttons()
        layout.addLayout(self.button_layout)

    def setup_keys_table(self):
        """Set up the API keys table."""
        self.keys_table = QTableWidget()
        self.keys_table.setColumnCount(4)
        self.keys_table.setHorizontalHeaderLabels([
            "Service", "Status", "Description", "Actions"
        ])

        # Configure table
        header = self.keys_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        self.keys_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.keys_table.setAlternatingRowColors(True)

        # Set minimum row height for better spacing
        self.keys_table.verticalHeader().setDefaultSectionSize(40)
        self.keys_table.verticalHeader().setVisible(False)

    def setup_add_edit_section(self):
        """Set up the add/edit key section."""
        self.add_edit_group = QGroupBox("Add/Edit API Key")
        layout = QVBoxLayout(self.add_edit_group)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)

        # Service selection
        service_layout = QHBoxLayout()
        service_layout.addWidget(QLabel("Service:"))

        self.service_combo = QComboBox()
        for service_id, service_info in self.services.items():
            self.service_combo.addItem(service_info["name"], service_id)
        service_layout.addWidget(self.service_combo)
        service_layout.addStretch()
        layout.addLayout(service_layout)

        # API Key input
        key_layout = QHBoxLayout()
        key_layout.addWidget(QLabel("API Key:"))

        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_input.setPlaceholderText("Enter your API key")
        key_layout.addWidget(self.api_key_input)

        # Show/Hide button
        self.show_key_btn = QPushButton("Show")
        self.show_key_btn.setCheckable(True)
        self.show_key_btn.clicked.connect(self.toggle_key_visibility)
        key_layout.addWidget(self.show_key_btn)

        layout.addLayout(key_layout)

        # Action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(10)

        self.save_key_btn = QPushButton("Save Key")
        self.save_key_btn.clicked.connect(self.save_api_key)
        action_layout.addWidget(self.save_key_btn)

        self.test_key_btn = QPushButton("Test Key")
        self.test_key_btn.clicked.connect(self.test_api_key)
        action_layout.addWidget(self.test_key_btn)

        self.clear_form_btn = QPushButton("Clear")
        self.clear_form_btn.clicked.connect(self.clear_form)
        action_layout.addWidget(self.clear_form_btn)

        action_layout.addStretch()
        layout.addLayout(action_layout)

        # Test progress
        self.test_progress = QProgressBar()
        self.test_progress.setVisible(False)
        layout.addWidget(self.test_progress)

        # Test result
        self.test_result = QLabel()
        self.test_result.setVisible(False)
        layout.addWidget(self.test_result)

    def setup_buttons(self):
        """Set up dialog buttons."""
        self.button_layout = QHBoxLayout()
        self.button_layout.setSpacing(10)

        self.refresh_btn = QPushButton("Refresh")
        self.refresh_btn.clicked.connect(self.load_existing_keys)
        self.button_layout.addWidget(self.refresh_btn)

        self.button_layout.addStretch()

        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.accept)
        self.button_layout.addWidget(self.close_btn)

    def load_existing_keys(self):
        """Load existing API keys into the table."""
        if not self.secure_storage:
            return

        stored_services = self.secure_storage.list_stored_services()

        # Clear table
        self.keys_table.setRowCount(0)

        # Show all services (including restricted ones) but with proper visual indicators
        all_services = set(self.all_services.keys()) | set(stored_services)

        for service_id in sorted(all_services):
            self.add_service_row(service_id, service_id in stored_services)

    def add_service_row(self, service_id: str, has_key: bool):
        """Add a service row to the table."""
        row = self.keys_table.rowCount()
        self.keys_table.insertRow(row)

        # Get service info (check both filtered and all services)
        service_info = self.all_services.get(service_id, {"name": service_id, "description": "Unknown service"})
        is_restricted = self._is_service_restricted(service_id)

        # Service name
        service_name = service_info["name"]
        if is_restricted:
            service_name += " (Pro Required)"

        service_item = QTableWidgetItem(service_name)
        service_item.setData(Qt.ItemDataRole.UserRole, service_id)

        # Gray out restricted services
        if is_restricted:
            service_item.setForeground(Qt.GlobalColor.gray)

        self.keys_table.setItem(row, 0, service_item)

        # Status
        if is_restricted:
            status_text = "Restricted (Pro Required)"
            status_color = Qt.GlobalColor.gray
        else:
            status_text = "Configured" if has_key else "Not configured"
            status_color = Qt.GlobalColor.green if has_key else Qt.GlobalColor.red

        status_item = QTableWidgetItem(status_text)
        status_item.setForeground(status_color)
        self.keys_table.setItem(row, 1, status_item)

        # Description
        desc_text = service_info["description"]
        if is_restricted:
            desc_text += " - Upgrade to Pro to access this service"

        desc_item = QTableWidgetItem(desc_text)
        if is_restricted:
            desc_item.setForeground(Qt.GlobalColor.gray)

        self.keys_table.setItem(row, 2, desc_item)

        # Actions
        actions_widget = self.create_actions_widget(service_id, has_key, is_restricted)
        self.keys_table.setCellWidget(row, 3, actions_widget)

    def create_actions_widget(self, service_id: str, has_key: bool, is_restricted: bool = False):
        """Create actions widget for a service row."""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(5)

        if is_restricted:
            # For restricted services, show upgrade button
            upgrade_btn = QPushButton("Upgrade to Pro")
            upgrade_btn.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    font-weight: bold;
                    padding: 4px 8px;
                    border-radius: 4px;
                    border: none;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
            """)
            upgrade_btn.clicked.connect(lambda: self._show_upgrade_dialog(service_id))
            layout.addWidget(upgrade_btn)
        elif has_key:
            edit_btn = QPushButton("Edit")
            edit_btn.clicked.connect(lambda: self.edit_key(service_id))
            layout.addWidget(edit_btn)

            delete_btn = QPushButton("Delete")
            delete_btn.clicked.connect(lambda: self.delete_key(service_id))
            layout.addWidget(delete_btn)

            test_btn = QPushButton("Test")
            test_btn.clicked.connect(lambda: self.test_existing_key(service_id))
            layout.addWidget(test_btn)
        else:
            add_btn = QPushButton("Add")
            add_btn.clicked.connect(lambda: self.add_key(service_id))
            layout.addWidget(add_btn)

        return widget

    def _show_upgrade_dialog(self, service_id: str):
        """Show upgrade dialog for restricted services."""
        service_info = self.all_services.get(service_id, {})
        service_name = service_info.get("name", service_id)

        QMessageBox.information(
            self,
            "Upgrade Required",
            f"{service_name} requires a Pro license.\n\n"
            "Upgrade to Pro to access all API providers and unlock unlimited image generation.\n\n"
            "Visit the License Activation dialog from Settings menu to upgrade your plan.",
            QMessageBox.StandardButton.Ok
        )

    def toggle_key_visibility(self):
        """Toggle API key visibility."""
        if self.show_key_btn.isChecked():
            self.api_key_input.setEchoMode(QLineEdit.EchoMode.Normal)
            self.show_key_btn.setText("Hide")
        else:
            self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.show_key_btn.setText("Show")

    def add_key(self, service_id: str):
        """Prepare form to add a new key."""
        # Check if service is restricted
        if self._is_service_restricted(service_id):
            self._show_upgrade_dialog(service_id)
            return

        # Set service in combo box
        for i in range(self.service_combo.count()):
            if self.service_combo.itemData(i) == service_id:
                self.service_combo.setCurrentIndex(i)
                break

        self.api_key_input.clear()
        self.api_key_input.setFocus()
        self.clear_test_result()

    def edit_key(self, service_id: str):
        """Prepare form to edit an existing key."""
        if not self.secure_storage:
            return

        # Check if service is restricted
        if self._is_service_restricted(service_id):
            self._show_upgrade_dialog(service_id)
            return

        # Set service in combo box
        for i in range(self.service_combo.count()):
            if self.service_combo.itemData(i) == service_id:
                self.service_combo.setCurrentIndex(i)
                break

        # Load existing key
        api_key = self.secure_storage.get_api_key(service_id)
        if api_key:
            self.api_key_input.setText(api_key)

        self.api_key_input.setFocus()
        self.clear_test_result()

    def delete_key(self, service_id: str):
        """Delete an API key."""
        if not self.secure_storage:
            return

        service_name = self.all_services.get(service_id, {}).get("name", service_id)

        reply = QMessageBox.question(
            self,
            "Delete API Key",
            f"Are you sure you want to delete the API key for {service_name}?\n\n"
            "This action cannot be undone.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.secure_storage.delete_api_key(service_id):
                QMessageBox.information(
                    self,
                    "Success",
                    f"API key for {service_name} has been deleted."
                )
                self.load_existing_keys()
            else:
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to delete API key for {service_name}."
                )

    def save_api_key(self):
        """Save the API key."""
        if not self.secure_storage:
            QMessageBox.critical(self, "Error", "Secure storage not available.")
            return

        service_id = self.service_combo.currentData()
        api_key = self.api_key_input.text().strip()

        if not api_key:
            QMessageBox.warning(self, "Warning", "Please enter an API key.")
            return

        # Check if service is restricted
        if self._is_service_restricted(service_id):
            self._show_upgrade_dialog(service_id)
            return

        if self.secure_storage.store_api_key(service_id, api_key):
            service_name = self.service_combo.currentText()
            QMessageBox.information(
                self,
                "Success",
                f"API key for {service_name} has been saved securely."
            )
            self.clear_form()
            self.load_existing_keys()
        else:
            QMessageBox.critical(
                self,
                "Error",
                "Failed to save API key. Please try again."
            )

    def test_api_key(self):
        """Test the API key in the form."""
        service_id = self.service_combo.currentData()
        api_key = self.api_key_input.text().strip()

        if not api_key:
            QMessageBox.warning(self, "Warning", "Please enter an API key to test.")
            return

        # Check if service is restricted
        if self._is_service_restricted(service_id):
            self._show_upgrade_dialog(service_id)
            return

        self._start_api_test(service_id, api_key)

    def test_existing_key(self, service_id: str):
        """Test an existing API key."""
        if not self.secure_storage:
            return

        # Check if service is restricted
        if self._is_service_restricted(service_id):
            self._show_upgrade_dialog(service_id)
            return

        api_key = self.secure_storage.get_api_key(service_id)
        if not api_key:
            QMessageBox.warning(self, "Warning", "No API key found for this service.")
            return

        self._start_api_test(service_id, api_key)

    def _start_api_test(self, service_id: str, api_key: str):
        """Start API key test in background thread."""
        self.test_progress.setVisible(True)
        self.test_progress.setRange(0, 0)  # Indeterminate progress
        self.test_result.setVisible(False)

        self.test_worker = ApiKeyTestWorker(service_id, api_key)
        self.test_worker.test_completed.connect(self._on_test_completed)
        self.test_worker.start()

    def _on_test_completed(self, service: str, success: bool, message: str):
        """Handle test completion."""
        self.test_progress.setVisible(False)
        self.test_result.setVisible(True)

        if success:
            self.test_result.setText(f"✓ {message}")
            self.test_result.setStyleSheet("color: green;")
        else:
            self.test_result.setText(f"✗ {message}")
            self.test_result.setStyleSheet("color: red;")

    def clear_form(self):
        """Clear the add/edit form."""
        self.api_key_input.clear()
        self.service_combo.setCurrentIndex(0)
        self.clear_test_result()

    def clear_test_result(self):
        """Clear test result display."""
        self.test_result.setVisible(False)
        self.test_progress.setVisible(False)
