#!/usr/bin/env python3
"""
Comprehensive test script for all license endpoints.
Tests the correct URL format that works with the server.
"""

import requests
import json
import platform
import hashlib

def test_all_endpoints():
    """Test all license endpoints with the correct URL format."""
    
    # Configuration
    license_server_url = "https://bulkimages.azanx.com/licensing/api"
    test_license_key = "BULKAI-67BD2EA2-CE1423CF-40039D04"  # Use the working license key from your test
    
    # Generate a test device ID
    hardware_string = f"{platform.machine()}-{platform.processor()}-{platform.system()}-{platform.node()}"
    device_id = hashlib.sha256(hardware_string.encode()).hexdigest()[:16]
    
    print(f"Testing all license endpoints...")
    print(f"Server URL: {license_server_url}")
    print(f"Device ID: {device_id}")
    print(f"License Key: {test_license_key}")
    print("=" * 60)
    
    # Test 1: Verify License
    print("\n1. Testing License Verification")
    print("-" * 40)
    try:
        response = requests.post(
            f"{license_server_url}/?action=verify-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Valid License: {result.get('valid', False)}")
            if result.get('valid'):
                license_info = result.get('license', {})
                print(f"Plan: {license_info.get('plan_name', 'Unknown')}")
                print(f"Daily Limit: {license_info.get('daily_image_limit', 'Unknown')}")
                usage_info = result.get('usage', {})
                print(f"Today's Usage: {usage_info.get('today_usage', 0)}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: License Activation
    print("\n2. Testing License Activation")
    print("-" * 40)
    try:
        device_info = {
            'system': platform.system(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version()
        }
        
        response = requests.post(
            f"{license_server_url}/?action=activate-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id,
                'device_name': platform.node(),
                'device_info': json.dumps(device_info)
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Activation Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'No message')}")
            if 'activation_date' in result:
                print(f"Activation Date: {result['activation_date']}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Usage Tracking
    print("\n3. Testing Usage Tracking")
    print("-" * 40)
    try:
        response = requests.post(
            f"{license_server_url}/?action=usage-tracking",
            json={
                'license_key': test_license_key,
                'device_id': device_id,
                'images_generated': 1,
                'api_calls': 1,
                'provider_used': 'together_ai',
                'model_used': 'flux_schnell'
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Tracking Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'No message')}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 4: License Info (GET request)
    print("\n4. Testing License Info")
    print("-" * 40)
    try:
        response = requests.get(
            f"{license_server_url}/?action=license-info&license_key={test_license_key}",
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"License Key: {result.get('license_key', 'Unknown')}")
            print(f"Customer: {result.get('customer_name', 'Unknown')}")
            print(f"Plan: {result.get('plan_name', 'Unknown')}")
            print(f"Status: {result.get('status', 'Unknown')}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "=" * 60)
    print("All endpoint tests completed!")

if __name__ == "__main__":
    test_all_endpoints()
