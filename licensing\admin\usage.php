<?php
require_once 'config.php';
checkAuth();

$pdo = getDatabase();

// Get usage statistics
$date_filter = $_GET['date'] ?? date('Y-m-d');
$license_filter = $_GET['license'] ?? '';

// Daily usage summary
$sql = "
    SELECT 
        DATE(ut.usage_date) as date,
        SUM(ut.images_generated) as total_images,
        COUNT(DISTINCT ut.license_id) as active_licenses,
        COUNT(DISTINCT ut.device_id) as active_devices
    FROM usage_tracking ut
    WHERE 1=1
";
$params = [];

if ($date_filter) {
    $sql .= " AND DATE(ut.usage_date) = ?";
    $params[] = $date_filter;
}

$sql .= " GROUP BY DATE(ut.usage_date) ORDER BY date DESC LIMIT 30";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$daily_stats = $stmt->fetchAll();

// Detailed usage by license
$detailed_sql = "
    SELECT 
        ut.*,
        l.license_key,
        c.name as customer_name,
        c.email,
        p.plan_name
    FROM usage_tracking ut
    JOIN licenses l ON ut.license_id = l.id
    JOIN customers c ON l.customer_id = c.id
    JOIN license_plans p ON l.plan_id = p.id
    WHERE 1=1
";
$detailed_params = [];

if ($date_filter) {
    $detailed_sql .= " AND DATE(ut.usage_date) = ?";
    $detailed_params[] = $date_filter;
}

if ($license_filter) {
    $detailed_sql .= " AND l.license_key LIKE ?";
    $detailed_params[] = "%$license_filter%";
}

$detailed_sql .= " ORDER BY ut.usage_date DESC, ut.images_generated DESC";

$stmt = $pdo->prepare($detailed_sql);
$stmt->execute($detailed_params);
$detailed_usage = $stmt->fetchAll();

// Top users this month
$stmt = $pdo->prepare("
    SELECT 
        c.name as customer_name,
        c.email,
        l.license_key,
        p.plan_name,
        SUM(ut.images_generated) as total_images,
        COUNT(DISTINCT ut.usage_date) as active_days
    FROM usage_tracking ut
    JOIN licenses l ON ut.license_id = l.id
    JOIN customers c ON l.customer_id = c.id
    JOIN license_plans p ON l.plan_id = p.id
    WHERE ut.usage_date >= DATE('now', 'start of month')
    GROUP BY ut.license_id
    ORDER BY total_images DESC
    LIMIT 10
");
$stmt->execute();
$top_users = $stmt->fetchAll();

// Overall statistics
$stmt = $pdo->prepare("
    SELECT 
        SUM(images_generated) as total_images_all_time,
        COUNT(DISTINCT license_id) as total_active_licenses,
        COUNT(DISTINCT device_id) as total_devices,
        COUNT(*) as total_usage_records
    FROM usage_tracking
");
$stmt->execute();
$overall_stats = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Usage Statistics - BulkAI License Admin</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="dashboard">
    <div class="header">
        <h1>BulkAI License Admin</h1>
        <nav class="nav">
            <a href="dashboard.php">Dashboard</a>
            <a href="customers.php">Customers</a>
            <a href="licenses.php">Licenses</a>
            <a href="usage.php" class="active">Usage</a>
            <a href="index.php?logout=1">Logout</a>
        </nav>
    </div>

    <div class="container">
        <!-- Overall Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3><?php echo number_format($overall_stats['total_images_all_time'] ?? 0); ?></h3>
                <p>Total Images Generated</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $overall_stats['total_active_licenses'] ?? 0; ?></h3>
                <p>Active Licenses</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $overall_stats['total_devices'] ?? 0; ?></h3>
                <p>Total Devices</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $overall_stats['total_usage_records'] ?? 0; ?></h3>
                <p>Usage Records</p>
            </div>
        </div>

        <!-- Filters -->
        <div class="table-container">
            <div class="table-header">
                <h2>Usage Filters</h2>
            </div>
            <div style="padding: 20px;">
                <form method="GET" style="display: flex; gap: 15px; align-items: end;">
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="date">Date</label>
                        <input type="date" id="date" name="date" value="<?php echo htmlspecialchars($date_filter); ?>">
                    </div>
                    <div class="form-group" style="margin-bottom: 0; flex: 1;">
                        <label for="license">License Key</label>
                        <input type="text" id="license" name="license" placeholder="Search by license key..." 
                               value="<?php echo htmlspecialchars($license_filter); ?>">
                    </div>
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="usage.php" class="btn btn-secondary">Clear</a>
                </form>
            </div>
        </div>

        <!-- Daily Usage Summary -->
        <div class="table-container">
            <div class="table-header">
                <h2>Daily Usage Summary</h2>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Total Images</th>
                        <th>Active Licenses</th>
                        <th>Active Devices</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($daily_stats as $stat): ?>
                    <tr>
                        <td><?php echo formatDate($stat['date']); ?></td>
                        <td><?php echo number_format($stat['total_images']); ?></td>
                        <td><?php echo $stat['active_licenses']; ?></td>
                        <td><?php echo $stat['active_devices']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                    
                    <?php if (empty($daily_stats)): ?>
                    <tr>
                        <td colspan="4" style="text-align: center; padding: 40px; color: #666;">
                            No usage data found for the selected criteria.
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Top Users This Month -->
        <div class="table-container">
            <div class="table-header">
                <h2>Top Users This Month</h2>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Customer</th>
                        <th>License Key</th>
                        <th>Plan</th>
                        <th>Total Images</th>
                        <th>Active Days</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($top_users as $user): ?>
                    <tr>
                        <td>
                            <?php echo htmlspecialchars($user['customer_name']); ?><br>
                            <small><?php echo htmlspecialchars($user['email']); ?></small>
                        </td>
                        <td><code style="font-size: 11px;"><?php echo htmlspecialchars($user['license_key']); ?></code></td>
                        <td><?php echo htmlspecialchars($user['plan_name']); ?></td>
                        <td><?php echo number_format($user['total_images']); ?></td>
                        <td><?php echo $user['active_days']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                    
                    <?php if (empty($top_users)): ?>
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 40px; color: #666;">
                            No usage data found for this month.
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Detailed Usage -->
        <div class="table-container">
            <div class="table-header">
                <h2>Detailed Usage</h2>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Customer</th>
                        <th>License Key</th>
                        <th>Plan</th>
                        <th>Images Generated</th>
                        <th>API Calls</th>
                        <th>Provider</th>
                        <th>Model</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($detailed_usage as $usage): ?>
                    <tr>
                        <td><?php echo formatDate($usage['usage_date']); ?></td>
                        <td>
                            <?php echo htmlspecialchars($usage['customer_name']); ?><br>
                            <small><?php echo htmlspecialchars($usage['email']); ?></small>
                        </td>
                        <td><code style="font-size: 11px;"><?php echo htmlspecialchars($usage['license_key']); ?></code></td>
                        <td><?php echo htmlspecialchars($usage['plan_name']); ?></td>
                        <td><?php echo number_format($usage['images_generated']); ?></td>
                        <td><?php echo number_format($usage['api_calls']); ?></td>
                        <td><?php echo htmlspecialchars($usage['provider_used'] ?? 'N/A'); ?></td>
                        <td><?php echo htmlspecialchars($usage['model_used'] ?? 'N/A'); ?></td>
                    </tr>
                    <?php endforeach; ?>
                    
                    <?php if (empty($detailed_usage)): ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                            No detailed usage data found for the selected criteria.
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
