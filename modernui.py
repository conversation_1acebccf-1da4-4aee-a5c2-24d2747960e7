import os
import sys
import traceback
import signal
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QSlider, QSpinBox, QComboBox, QFileDialog, 
    QMessageBox, QSplitter, QFrame, QScrollArea, QSizePolicy, QDialog, 
    QTabWidget, QMenuBar, QStatusBar, QProgressBar, QTextEdit, QGridLayout,
    QGroupBox, QCheckBox, QSpacerItem
)
from PyQt6.QtCore import Qt, QTimer, QEventLoop, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QKeySequence, QPixmap, QFont, QPalette, QColor

from ui.styles import Styles
from ui.api_key_manager import Api<PERSON>eyManagerDialog
from config_manager import ConfigManager
from app_controller import AppController
from usage_tracker import UsageTracker
from logger import get_logger


class ModernCard(QFrame):
    """A modern card-style container widget."""
    
    def __init__(self, title=None, parent=None):
        super().__init__(parent)
        self.setObjectName("modernCard")
        self.setup_ui(title)
        
    def setup_ui(self, title):
        """Set up the card UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        if title:
            title_label = QLabel(title)
            title_label.setObjectName("cardTitle")
            layout.addWidget(title_label)
            
        self.content_layout = QVBoxLayout()
        layout.addLayout(self.content_layout)
        
    def add_widget(self, widget):
        """Add a widget to the card content."""
        self.content_layout.addWidget(widget)
        
    def add_layout(self, layout):
        """Add a layout to the card content."""
        self.content_layout.addLayout(layout)


class ModernFormRow(QWidget):
    """A modern form row with label and control."""
    
    def __init__(self, label_text, control_widget, parent=None):
        super().__init__(parent)
        self.setup_ui(label_text, control_widget)
        
    def setup_ui(self, label_text, control_widget):
        """Set up the form row UI."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # Label
        label = QLabel(label_text)
        label.setObjectName("formLabel")
        label.setMinimumWidth(140)
        label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        
        # Control
        control_widget.setObjectName("formControl")
        
        layout.addWidget(label)
        layout.addWidget(control_widget, 1)


class ModernButton(QPushButton):
    """A modern styled button."""
    
    def __init__(self, text, button_type="primary", parent=None):
        super().__init__(text, parent)
        self.setObjectName(f"modernButton{button_type.capitalize()}")
        self.setMinimumHeight(44)
        self.setFont(QFont("Segoe UI", 10, QFont.Weight.Medium))


class ModernProgressCard(ModernCard):
    """A modern card for displaying progress information."""
    
    def __init__(self, parent=None):
        super().__init__("Generation Progress", parent)
        self.setup_progress_ui()
        
    def setup_progress_ui(self):
        """Set up the progress UI elements."""
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("modernProgressBar")
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.add_widget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready to generate")
        self.status_label.setObjectName("progressStatus")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.add_widget(self.status_label)
        
        # Usage counter
        self.usage_label = QLabel("Images generated today: 0")
        self.usage_label.setObjectName("usageCounter")
        self.usage_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.add_widget(self.usage_label)
        
    def update_progress(self, value, text=None):
        """Update progress bar value and text."""
        self.progress_bar.setValue(value)
        if text:
            self.status_label.setText(text)
            
    def update_usage(self, current, limit):
        """Update usage counter display."""
        if limit == -1:
            self.usage_label.setText(f"Images generated today: {current} (Unlimited)")
        else:
            self.usage_label.setText(f"Images generated today: {current}/{limit}")


class ModernLicenseBanner(QFrame):
    """A modern license status banner."""
    
    upgrade_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("modernLicenseBanner")
        self.setMaximumHeight(60)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the banner UI."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(15)
        
        # Status icon and text
        self.status_label = QLabel("Checking license status...")
        self.status_label.setObjectName("bannerStatus")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # Upgrade button (initially hidden)
        self.upgrade_button = ModernButton("Upgrade to Pro", "accent")
        self.upgrade_button.clicked.connect(self.upgrade_clicked.emit)
        self.upgrade_button.hide()
        layout.addWidget(self.upgrade_button)
        
    def update_status(self, is_trial, status_text):
        """Update the banner status."""
        self.status_label.setText(status_text)
        
        if is_trial:
            self.upgrade_button.show()
            self.setStyleSheet("""
                QFrame#modernLicenseBanner {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #fff3cd, stop:1 #ffeaa7);
                    border: 1px solid #ffeaa7;
                    border-radius: 8px;
                }
                QLabel#bannerStatus {
                    color: #856404;
                    font-weight: 600;
                }
            """)
        else:
            self.upgrade_button.hide()
            self.setStyleSheet("""
                QFrame#modernLicenseBanner {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #d4edda, stop:1 #c3e6cb);
                    border: 1px solid #c3e6cb;
                    border-radius: 8px;
                }
                QLabel#bannerStatus {
                    color: #155724;
                    font-weight: 600;
                }
            """)


class ModernLicenseActivationDialog(QDialog):
    """Modern dialog for license activation."""

    def __init__(self, parent=None, license_manager=None):
        """Initialize the license activation dialog."""
        super().__init__(parent)
        self.license_manager = license_manager

        self.setWindowTitle("License Activation")
        self.setMinimumWidth(600)
        self.setMinimumHeight(500)
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # Header
        header_card = ModernCard()
        header_layout = QVBoxLayout()

        title_label = QLabel("🚀 Activate Your BulkAI License")
        title_label.setObjectName("dialogTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)

        # Current status
        self.status_label = QLabel()
        self.status_label.setObjectName("statusInfo")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(self.status_label)

        header_card.add_layout(header_layout)
        layout.addWidget(header_card)

        # License key input card
        input_card = ModernCard("License Key")
        self.key_input = QLineEdit()
        self.key_input.setPlaceholderText("BULKAI-XXXXXXXX-XXXXXXXX-XXXXXXXX")
        self.key_input.setMinimumHeight(50)
        input_card.add_widget(self.key_input)
        layout.addWidget(input_card)

        # Plan information card
        plans_card = ModernCard("Available Plans")
        plans_text = """
        <div style="line-height: 1.6;">
        <p><strong>🆓 Free Plan:</strong> 10 images/day, Flux Schnell model only, Together AI only</p>
        <p><strong>💎 Monthly Pro ($29.99):</strong> Unlimited images, all models and providers</p>
        <p><strong>💎 Yearly Pro ($299.99):</strong> Unlimited images, all models and providers</p>
        <p><strong>💎 Lifetime Pro ($999.99):</strong> One-time purchase, unlimited access</p>
        </div>
        """
        plans_label = QLabel(plans_text)
        plans_label.setWordWrap(True)
        plans_label.setObjectName("planInfo")
        plans_card.add_widget(plans_label)
        layout.addWidget(plans_card)

        # Buttons
        button_layout = QHBoxLayout()

        # Delete license button (only show if license is active)
        self.delete_button = ModernButton("Remove License", "danger")
        self.delete_button.clicked.connect(self._delete_license)

        self.close_button = ModernButton("Close", "secondary")
        self.close_button.clicked.connect(self.reject)

        self.activate_button = ModernButton("Activate License", "primary")
        self.activate_button.clicked.connect(self._activate_license)

        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        button_layout.addWidget(self.activate_button)
        layout.addLayout(button_layout)

        # Initial status update
        self._update_status_display()

    def _update_status_display(self):
        """Update the license status display."""
        if not self.license_manager:
            self.status_label.setText("License manager not available")
            return

        verification = self.license_manager.verify_license()
        if verification.get('valid'):
            license_info = verification.get('license', {})
            plan_name = license_info.get('plan_name', 'Unknown')
            daily_limit = license_info.get('daily_image_limit', 0)
            limit_text = "Unlimited" if daily_limit == -1 else str(daily_limit)

            usage_info = verification.get('usage', {})
            today_usage = usage_info.get('today_usage', 0)

            self.status_label.setText(
                f"✅ <b>Status:</b> Active License<br>"
                f"📋 <b>Plan:</b> {plan_name}<br>"
                f"📊 <b>Daily Limit:</b> {limit_text}<br>"
                f"🎯 <b>Today's Usage:</b> {today_usage}"
            )
        else:
            self.status_label.setText(
                "⚠️ <b>Status:</b> Free Plan (No License)<br>"
                "📊 <b>Daily Limit:</b> 10 images<br>"
                "🔒 <b>Features:</b> Flux Schnell model only, Together AI only"
            )

        # Update button visibility
        self._update_button_visibility()

    def _update_button_visibility(self):
        """Update button visibility based on license status."""
        if not hasattr(self, 'delete_button'):
            return

        if not self.license_manager:
            self.delete_button.hide()
            return

        verification = self.license_manager.verify_license()
        has_license = verification.get('valid', False)

        # Show delete button only if there's an active license
        if has_license:
            self.delete_button.show()
        else:
            self.delete_button.hide()

    def _activate_license(self):
        """Activate the entered license key."""
        license_key = self.key_input.text().strip()
        if not license_key:
            QMessageBox.warning(self, "Error", "Please enter a license key.")
            return

        # Show progress
        self.activate_button.setEnabled(False)
        self.activate_button.setText("Activating...")

        try:
            result = self.license_manager.activate_license(license_key)

            if result.get('success'):
                QMessageBox.information(
                    self,
                    "Success",
                    "License activated successfully! All Pro features are now unlocked."
                )
                self._update_status_display()
                self.key_input.clear()
                self.accept()
            else:
                error_msg = result.get('error', 'Unknown error occurred')
                QMessageBox.critical(
                    self,
                    "Activation Failed",
                    f"Failed to activate license:\n{error_msg}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred during activation:\n{str(e)}"
            )
        finally:
            self.activate_button.setEnabled(True)
            self.activate_button.setText("Activate License")

    def _delete_license(self):
        """Delete/deactivate the current license."""
        if not self.license_manager:
            QMessageBox.warning(self, "Error", "License manager not available.")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Remove License",
            "Are you sure you want to remove the license from this device?\n\n"
            "This will:\n"
            "• Deactivate the license on this device\n"
            "• Return you to the free plan\n"
            "• Allow you to use the license on another device\n\n"
            "You can reactivate the license later if needed.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Show progress
        self.delete_button.setEnabled(False)
        self.delete_button.setText("Removing...")

        try:
            result = self.license_manager.deactivate_license()

            if result.get('success'):
                QMessageBox.information(
                    self,
                    "License Removed",
                    "License has been successfully removed from this device.\n"
                    "You are now on the free plan."
                )
            else:
                # If server deactivation fails, offer local removal
                reply = QMessageBox.question(
                    self,
                    "Server Deactivation Failed",
                    f"Failed to deactivate license on server:\n{result.get('error', 'Unknown error')}\n\n"
                    "Would you like to remove the license locally only?\n"
                    "Note: This won't free up the license slot on the server.",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    if self.license_manager.delete_license_locally():
                        QMessageBox.information(
                            self,
                            "License Removed Locally",
                            "License has been removed from this device locally.\n"
                            "You are now on the free plan.\n\n"
                            "Note: The license slot may still be occupied on the server."
                        )
                    else:
                        QMessageBox.critical(
                            self,
                            "Error",
                            "Failed to remove license locally."
                        )

            # Update UI regardless of success/failure
            self._update_status_display()
            self.key_input.clear()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred while removing the license:\n{str(e)}"
            )
        finally:
            self.delete_button.setEnabled(True)
            self.delete_button.setText("Remove License")


class ModernUpgradeDialog(QDialog):
    """Modern dialog for prompting users to upgrade to Pro plan."""

    def __init__(self, parent=None, message="", feature_name="this feature"):
        """Initialize the upgrade dialog."""
        super().__init__(parent)
        self.setWindowTitle("Upgrade Required")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        self.setup_ui(message, feature_name)

    def setup_ui(self, message, feature_name):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # Header card
        header_card = ModernCard()
        header_layout = QVBoxLayout()

        title_label = QLabel("🚀 Upgrade to Pro")
        title_label.setObjectName("dialogTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)

        # Message
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setObjectName("upgradeMessage")
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(message_label)

        header_card.add_layout(header_layout)
        layout.addWidget(header_card)

        # Features card
        features_card = ModernCard("Pro Plan Benefits")
        benefits_text = """
        <div style="line-height: 1.8;">
        <p>✅ <strong>Unlimited daily image generation</strong></p>
        <p>✅ <strong>Access to all AI models</strong> (Runware AI, FLUX variants)</p>
        <p>✅ <strong>Bulk generation feature</strong> for processing multiple prompts</p>
        <p>✅ <strong>All API providers</strong> (Together AI + Runware AI + Replicate AI)</p>
        <p>✅ <strong>Priority support</strong> and updates</p>
        <p>✅ <strong>No daily limits or restrictions</strong></p>
        </div>
        """
        benefits_label = QLabel(benefits_text)
        benefits_label.setWordWrap(True)
        benefits_label.setObjectName("benefitsList")
        features_card.add_widget(benefits_label)
        layout.addWidget(features_card)

        # Pricing card
        pricing_card = ModernCard("Choose Your Plan")
        pricing_text = """
        <div style="line-height: 1.6;">
        <p><strong>💳 Monthly Pro:</strong> $29.99/month</p>
        <p><strong>💰 Yearly Pro:</strong> $299.99/year <em>(Save 17%)</em></p>
        <p><strong>💎 Lifetime Pro:</strong> $999.99 <em>(One-time payment)</em></p>
        </div>
        """
        pricing_label = QLabel(pricing_text)
        pricing_label.setWordWrap(True)
        pricing_label.setObjectName("pricingInfo")
        pricing_card.add_widget(pricing_label)
        layout.addWidget(pricing_card)

        # Buttons
        button_layout = QHBoxLayout()

        self.later_button = ModernButton("Maybe Later", "secondary")
        self.later_button.clicked.connect(self.reject)

        self.upgrade_button = ModernButton("Upgrade Now", "accent")
        self.upgrade_button.clicked.connect(self.accept)

        button_layout.addWidget(self.later_button)
        button_layout.addStretch()
        button_layout.addWidget(self.upgrade_button)
        layout.addLayout(button_layout)


class ModernApiKeyDialog(QDialog):
    """Modern dialog for entering API keys."""

    def __init__(self, parent=None, config_manager=None, license_manager=None):
        """Initialize the API key dialog."""
        super().__init__(parent)
        self.config_manager = config_manager
        self.license_manager = license_manager

        self.setWindowTitle("API Key Configuration")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # Header
        header_card = ModernCard()
        header_layout = QVBoxLayout()

        title_label = QLabel("🔑 Configure API Keys")
        title_label.setObjectName("dialogTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)

        header_card.add_layout(header_layout)
        layout.addWidget(header_card)

        # Together AI API key card
        together_card = ModernCard("Together AI API Key")
        self.together_input = QLineEdit()
        self.together_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.together_input.setPlaceholderText("Enter your Together AI API key")
        self.together_input.setMinimumHeight(50)

        # Load existing key if available
        if self.config_manager:
            api_key = self.config_manager.get_api_key("together_ai")
            if api_key:
                self.together_input.setText(api_key)

        together_card.add_widget(self.together_input)
        layout.addWidget(together_card)

        # Check if user can access Runware AI
        show_runware = True
        if self.license_manager and self.license_manager.is_trial_plan():
            show_runware = False

        # Runware AI API key (only show for Pro users)
        if show_runware:
            runware_card = ModernCard("Runware AI API Key")
            self.runware_input = QLineEdit()
            self.runware_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.runware_input.setPlaceholderText("Enter your Runware AI API key")
            self.runware_input.setMinimumHeight(50)

            # Load existing key if available
            if self.config_manager:
                api_key = self.config_manager.get_api_key("runware_ai")
                if api_key:
                    self.runware_input.setText(api_key)

            runware_card.add_widget(self.runware_input)
            layout.addWidget(runware_card)
        else:
            # Create a hidden input for trial users
            self.runware_input = QLineEdit()
            self.runware_input.hide()

            # Show trial limitation message
            trial_card = ModernCard("Runware AI")
            trial_message = QLabel("🔒 Runware AI is available with Pro plans only")
            trial_message.setObjectName("trialMessage")
            trial_card.add_widget(trial_message)
            layout.addWidget(trial_card)

        # Links card
        links_card = ModernCard("Get API Keys")
        links_layout = QVBoxLayout()

        together_link = QLabel("<a href='https://api.together.xyz/settings/api-keys'>🔗 Together AI API Keys</a>")
        together_link.setOpenExternalLinks(True)
        together_link.setObjectName("apiLink")
        links_layout.addWidget(together_link)

        if show_runware:
            runware_link = QLabel("<a href='https://my.runware.ai/'>🔗 Runware AI API Keys</a>")
            runware_link.setOpenExternalLinks(True)
            runware_link.setObjectName("apiLink")
            links_layout.addWidget(runware_link)

        links_card.add_layout(links_layout)
        layout.addWidget(links_card)

        # Buttons
        button_layout = QHBoxLayout()
        self.cancel_button = ModernButton("Cancel", "secondary")
        self.cancel_button.clicked.connect(self.reject)

        self.save_button = ModernButton("Save", "primary")
        self.save_button.clicked.connect(self.accept)

        button_layout.addWidget(self.cancel_button)
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        layout.addLayout(button_layout)

    def get_together_api_key(self):
        """Get the entered Together AI API key."""
        return self.together_input.text().strip()

    def get_runware_api_key(self):
        """Get the entered Runware AI API key."""
        return self.runware_input.text().strip()


class ModernMainWindow(QMainWindow):
    """Modern main window for the BulkAI application."""

    def __init__(self):
        """Initialize the main window."""
        super().__init__()

        # Initialize logger
        self.logger = get_logger()
        self.logger.info("Initializing BulkAI Modern UI")

        # Initialize config manager
        self.config_manager = ConfigManager()
        self.logger.debug("Config manager initialized")

        # Initialize controller
        self.controller = AppController()
        self.logger.debug("App controller initialized")

        # Initialize usage tracker
        self.usage_tracker = UsageTracker(self.config_manager, self.controller.license_manager)
        self.logger.debug("Usage tracker initialized")

        # Flag to prevent double counting of usage
        self._generation_completed = False

        # Connect controller signals
        self._connect_controller_signals()

        # Connect usage tracker signals
        self._connect_usage_tracker_signals()

        # Set up the window
        self.setWindowTitle("BulkAI Modern UI - Unlimited AI Images. One Click Away.")
        self.setMinimumSize(1400, 900)

        # Set window icon
        app_icon = QIcon("ui/resources/app-logo.svg")
        self.setWindowIcon(app_icon)

        # Set up the UI
        self._setup_ui()
        self._setup_menu()
        self._setup_status_bar()
        self._apply_modern_theme()
        self.logger.debug("Modern UI setup complete")

        # Check for API key
        self._check_api_key()

        # Populate both model dropdowns
        self._populate_model_dropdown()
        self._populate_bulk_model_dropdown()

        # Update license status display
        self._update_license_status()

        # Check bulk generation access on startup
        self._check_bulk_generation_access()

    def _connect_controller_signals(self):
        """Connect controller signals to UI slots."""
        # Status updates
        self.controller.status_changed.connect(self._update_status)

        # Image generation
        self.controller.image_generated.connect(self._display_image)
        self.controller.generation_started.connect(self._on_generation_started)
        self.controller.generation_progress.connect(self._on_generation_progress)
        self.controller.generation_finished.connect(self._on_generation_finished)

        # Bulk generation
        self.controller.bulk_generation_started.connect(self._on_bulk_generation_started)
        self.controller.bulk_generation_progress.connect(self._on_bulk_generation_progress)
        self.controller.bulk_generation_finished.connect(self._on_bulk_generation_finished)
        self.controller.bulk_prompt_processing.connect(self._on_bulk_prompt_processing)

        # Error handling
        self.controller.error_occurred.connect(self._show_error)

    def _connect_usage_tracker_signals(self):
        """Connect usage tracker signals to UI slots."""
        # Usage updates
        self.usage_tracker.usage_updated.connect(self._on_usage_updated)
        self.usage_tracker.limit_exceeded.connect(self._on_limit_exceeded)
        self.usage_tracker.limit_warning.connect(self._on_limit_warning)
        self.usage_tracker.reset_occurred.connect(self._on_usage_reset)

    def _setup_ui(self):
        """Set up the modern user interface."""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(20)

        # Add license status banner
        self.license_banner = ModernLicenseBanner()
        self.license_banner.upgrade_clicked.connect(self._show_upgrade_dialog)
        main_layout.addWidget(self.license_banner)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("modernTabWidget")
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        main_layout.addWidget(self.tab_widget)

        # Create image generator tab
        self.image_generator_tab = QWidget()
        self.tab_widget.addTab(self.image_generator_tab, "🎨 Image Generator")

        # Set up image generator tab
        self._setup_image_generator_tab()

        # Create bulk generation tab
        self.bulk_generation_tab = QWidget()
        self.tab_widget.addTab(self.bulk_generation_tab, "📦 Bulk Generation")

        # Set up bulk generation tab
        self._setup_bulk_generation_tab()

    def _setup_image_generator_tab(self):
        """Set up the modern image generator tab."""
        # Create layout
        layout = QHBoxLayout(self.image_generator_tab)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)

        # Left panel - Controls
        left_scroll = QScrollArea()
        left_scroll.setWidgetResizable(True)
        left_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        left_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        left_scroll.setMinimumWidth(400)
        left_scroll.setMaximumWidth(500)
        left_scroll.setObjectName("modernScrollArea")

        # Left panel content
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(20)

        # Prompt card
        prompt_card = ModernCard("✨ Create Your Image")
        self.prompt_input = QLineEdit()
        self.prompt_input.setPlaceholderText("Describe the image you want to generate...")
        self.prompt_input.setMinimumHeight(60)
        self.prompt_input.setObjectName("modernPromptInput")
        self.prompt_input.textChanged.connect(self._on_prompt_changed)
        prompt_card.add_widget(self.prompt_input)
        left_layout.addWidget(prompt_card)

        # Settings card
        settings_card = ModernCard("⚙️ Generation Settings")
        self._setup_generation_settings(settings_card)
        left_layout.addWidget(settings_card)

        # Progress card
        self.progress_card = ModernProgressCard()
        left_layout.addWidget(self.progress_card)

        # Action buttons card
        actions_card = ModernCard("🚀 Actions")
        self._setup_action_buttons(actions_card)
        left_layout.addWidget(actions_card)

        left_layout.addStretch()

        # Set the content widget to scroll area
        left_scroll.setWidget(left_panel)

        # Right panel - Image display
        right_panel = self._create_image_display_panel()

        # Add panels to layout
        layout.addWidget(left_scroll)
        layout.addWidget(right_panel, 1)

    def _setup_generation_settings(self, settings_card):
        """Set up the generation settings in the card."""
        # API Provider
        provider_row = ModernFormRow("API Provider:", QComboBox())
        self.provider_combo = provider_row.findChild(QComboBox)

        # Check license status to determine available providers
        license_manager = self.controller.license_manager
        if license_manager.is_trial_plan():
            # Trial users only get Together AI
            self.provider_combo.addItems(["Together AI"])
            self.provider_combo.setCurrentIndex(0)
            # Force Together AI for trial users
            self.config_manager.set_setting("default_provider", "together_ai")
        else:
            # Pro users get all providers
            self.provider_combo.addItems([
                "Together AI",
                "Runware AI",
                "Replicate AI"
            ])
            # Set the current provider from settings
            current_provider = self.config_manager.get_setting("default_provider", "together_ai")
            if current_provider == "together_ai":
                self.provider_combo.setCurrentIndex(0)
            elif current_provider == "runware_ai":
                self.provider_combo.setCurrentIndex(1)
            elif current_provider == "replicate_ai":
                self.provider_combo.setCurrentIndex(2)
            else:
                self.provider_combo.setCurrentIndex(0)  # Default to Together AI

        self.provider_combo.currentIndexChanged.connect(self._on_provider_changed)
        settings_card.add_widget(provider_row)

        # Model selection
        model_row = ModernFormRow("Model:", QComboBox())
        self.model_combo = model_row.findChild(QComboBox)
        self.model_combo.currentTextChanged.connect(self._on_model_changed)
        settings_card.add_widget(model_row)

        # Image Style selection
        style_row = ModernFormRow("Image Style:", QComboBox())
        self.style_combo = style_row.findChild(QComboBox)
        self.style_combo.addItems([
            "None",
            "Photorealistic",
            "Digital Art",
            "Oil Painting",
            "Watercolor",
            "Anime/Manga",
            "Sketch/Drawing",
            "Cinematic",
            "Fantasy Art",
            "Abstract",
            "Vintage/Retro"
        ])
        # Set default style from config
        default_style = self.config_manager.get_setting("default_image_style", "None")
        style_index = self.style_combo.findText(default_style)
        if style_index >= 0:
            self.style_combo.setCurrentIndex(style_index)
        self.style_combo.currentTextChanged.connect(self._on_style_changed)
        settings_card.add_widget(style_row)

        # Resolution type
        res_type_row = ModernFormRow("Resolution:", QComboBox())
        self.res_type_combo = res_type_row.findChild(QComboBox)
        self.res_type_combo.addItems([
            "Square (1:1)",
            "Landscape (16:9)",
            "Portrait (9:16)",
            "Custom"
        ])
        self.res_type_combo.currentIndexChanged.connect(self._on_resolution_type_changed)
        settings_card.add_widget(res_type_row)

        # Image size
        size_row = ModernFormRow("Image Size:", QComboBox())
        self.size_combo = size_row.findChild(QComboBox)
        settings_card.add_widget(size_row)

        # Custom dimensions (initially hidden)
        custom_widget = QWidget()
        custom_layout = QHBoxLayout(custom_widget)
        custom_layout.setContentsMargins(0, 0, 0, 0)

        self.width_spin = QSpinBox()
        self.width_spin.setMinimum(64)
        self.width_spin.setMaximum(1408)
        self.width_spin.setSingleStep(64)
        self.width_spin.setValue(1024)
        self.width_spin.valueChanged.connect(self._adjust_width_to_multiple_of_64)

        self.height_spin = QSpinBox()
        self.height_spin.setMinimum(64)
        self.height_spin.setMaximum(1408)
        self.height_spin.setSingleStep(64)
        self.height_spin.setValue(768)
        self.height_spin.valueChanged.connect(self._adjust_height_to_multiple_of_64)

        custom_layout.addWidget(QLabel("Width:"))
        custom_layout.addWidget(self.width_spin)
        custom_layout.addWidget(QLabel("Height:"))
        custom_layout.addWidget(self.height_spin)

        self.custom_dimensions_row = ModernFormRow("Custom Size:", custom_widget)
        self.custom_dimensions_row.setVisible(False)
        settings_card.add_widget(self.custom_dimensions_row)

        # Initialize resolution options
        self._populate_size_options()

        # Steps
        steps_widget = QWidget()
        steps_layout = QHBoxLayout(steps_widget)
        steps_layout.setContentsMargins(0, 0, 0, 0)

        self.steps_slider = QSlider(Qt.Orientation.Horizontal)
        self.steps_slider.setMinimum(1)
        self.steps_slider.setMaximum(50)
        self.steps_slider.setValue(28)
        self.steps_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.steps_slider.setTickInterval(5)

        self.steps_spin = QSpinBox()
        self.steps_spin.setMinimum(1)
        self.steps_spin.setMaximum(50)
        self.steps_spin.setValue(28)

        steps_layout.addWidget(self.steps_slider, 1)
        steps_layout.addWidget(self.steps_spin)

        # Connect steps slider and spin box
        self.steps_slider.valueChanged.connect(self.steps_spin.setValue)
        self.steps_spin.valueChanged.connect(self.steps_slider.setValue)

        steps_row = ModernFormRow("Steps:", steps_widget)
        settings_card.add_widget(steps_row)

        # Seed
        seed_row = ModernFormRow("Seed:", QSpinBox())
        self.seed_spin = seed_row.findChild(QSpinBox)
        self.seed_spin.setMinimum(-1)
        self.seed_spin.setMaximum(9999999)
        self.seed_spin.setValue(-1)
        self.seed_spin.setSpecialValueText("Random")
        settings_card.add_widget(seed_row)

    def _setup_action_buttons(self, actions_card):
        """Set up the action buttons in the card."""
        button_layout = QHBoxLayout()

        # Generate button
        self.generate_button = ModernButton("🎨 Generate Image", "primary")
        self.generate_button.clicked.connect(self._generate_image)

        # Save button
        self.save_button = ModernButton("💾 Save Image", "secondary")
        self.save_button.setEnabled(False)
        self.save_button.clicked.connect(self._save_image)

        button_layout.addWidget(self.generate_button)
        button_layout.addWidget(self.save_button)
        actions_card.add_layout(button_layout)

    def _create_image_display_panel(self):
        """Create the image display panel."""
        # Image display card
        image_card = ModernCard("🖼️ Generated Image")

        # Image display
        self.image_label = QLabel("No image generated yet")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setMinimumSize(600, 600)
        self.image_label.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )
        self.image_label.setObjectName("modernImageDisplay")

        # Scroll area for image
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.image_label)
        scroll_area.setWidgetResizable(True)
        scroll_area.setObjectName("modernImageScrollArea")

        image_card.add_widget(scroll_area)
        return image_card

    def _apply_modern_theme(self):
        """Apply the modern theme to the application."""
        # Get the current theme
        theme = self.config_manager.get_setting("theme", "dark")

        # Base theme from existing styles
        if theme == "dark":
            base_style = Styles.get_dark_theme()
        else:
            base_style = Styles.get_light_theme()

        # Modern enhancements
        modern_style = """
            /* Modern Card Styling */
            QFrame#modernCard {
                background-color: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 0px;
                margin: 5px;
            }

            QLabel#cardTitle {
                font-size: 18px;
                font-weight: 700;
                color: #ffffff;
                margin-bottom: 10px;
                padding: 5px 0px;
            }

            /* Modern Form Elements */
            QLabel#formLabel {
                font-size: 14px;
                font-weight: 600;
                color: #e0e0e0;
            }

            QWidget#formControl {
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
            }

            /* Modern Buttons */
            QPushButton#modernButtonPrimary {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4f46e5, stop:1 #3730a3);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }

            QPushButton#modernButtonPrimary:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5b52e8, stop:1 #4338ca);
                transform: translateY(-1px);
            }

            QPushButton#modernButtonPrimary:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3730a3, stop:1 #312e81);
            }

            QPushButton#modernButtonSecondary {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #374151, stop:1 #1f2937);
                color: #e5e7eb;
                border: 1px solid #4b5563;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }

            QPushButton#modernButtonSecondary:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4b5563, stop:1 #374151);
                border-color: #6b7280;
            }

            QPushButton#modernButtonAccent {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b, stop:1 #d97706);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }

            QPushButton#modernButtonAccent:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fbbf24, stop:1 #f59e0b);
            }

            QPushButton#modernButtonDanger {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ef4444, stop:1 #dc2626);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
            }

            QPushButton#modernButtonDanger:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f87171, stop:1 #ef4444);
            }

            /* Modern Progress Bar */
            QProgressBar#modernProgressBar {
                border: none;
                border-radius: 8px;
                background-color: #374151;
                text-align: center;
                font-weight: 600;
                color: white;
                height: 24px;
            }

            QProgressBar#modernProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4f46e5, stop:1 #7c3aed);
                border-radius: 8px;
            }

            /* Modern Input Fields */
            QLineEdit#modernPromptInput {
                background-color: rgba(255, 255, 255, 0.05);
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 16px 20px;
                font-size: 16px;
                color: #ffffff;
                selection-background-color: #4f46e5;
            }

            QLineEdit#modernPromptInput:focus {
                border-color: #4f46e5;
                background-color: rgba(255, 255, 255, 0.08);
            }

            /* Modern Tab Widget */
            QTabWidget#modernTabWidget::pane {
                border: none;
                background-color: transparent;
            }

            QTabWidget#modernTabWidget QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.05), stop:1 rgba(255, 255, 255, 0.02));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-bottom: none;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                padding: 12px 24px;
                margin-right: 4px;
                font-weight: 600;
                font-size: 14px;
                color: #9ca3af;
            }

            QTabWidget#modernTabWidget QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4f46e5, stop:1 #3730a3);
                color: white;
                border-color: #4f46e5;
            }

            QTabWidget#modernTabWidget QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08), stop:1 rgba(255, 255, 255, 0.05));
                color: #e5e7eb;
            }

            /* Modern Scroll Areas */
            QScrollArea#modernScrollArea {
                border: none;
                background-color: transparent;
            }

            QScrollArea#modernImageScrollArea {
                border: none;
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 12px;
            }

            /* Modern Image Display */
            QLabel#modernImageDisplay {
                background-color: rgba(255, 255, 255, 0.02);
                border: 2px dashed rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                color: #9ca3af;
                font-size: 16px;
                font-weight: 500;
            }

            /* Dialog Styling */
            QLabel#dialogTitle {
                font-size: 24px;
                font-weight: 700;
                color: #4f46e5;
                margin: 10px 0px;
            }

            QLabel#statusInfo, QLabel#upgradeMessage, QLabel#planInfo,
            QLabel#benefitsList, QLabel#pricingInfo {
                font-size: 14px;
                line-height: 1.6;
                color: #e5e7eb;
            }

            QLabel#trialMessage {
                background-color: rgba(251, 191, 36, 0.1);
                border: 1px solid rgba(251, 191, 36, 0.3);
                border-radius: 8px;
                padding: 12px;
                color: #fbbf24;
                font-weight: 600;
            }

            QLabel#apiLink {
                color: #60a5fa;
                font-size: 14px;
                font-weight: 500;
            }

            QLabel#apiLink:hover {
                color: #93c5fd;
            }

            /* Usage and Progress Labels */
            QLabel#progressStatus, QLabel#usageCounter {
                font-size: 14px;
                font-weight: 500;
                color: #d1d5db;
                margin: 5px 0px;
            }
        """

        # Combine base theme with modern enhancements
        combined_style = base_style + modern_style
        self.setStyleSheet(combined_style)

    # Placeholder methods that need to be implemented to match the original functionality
    def _setup_bulk_generation_tab(self):
        """Set up the bulk generation tab (placeholder)."""
        # Create a simple placeholder for now
        layout = QVBoxLayout(self.bulk_generation_tab)
        layout.setContentsMargins(25, 25, 25, 25)

        placeholder_card = ModernCard("📦 Bulk Generation")
        placeholder_label = QLabel("Bulk generation functionality will be implemented here.\nThis feature allows processing multiple prompts from a file.")
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setObjectName("placeholderText")
        placeholder_card.add_widget(placeholder_label)
        layout.addWidget(placeholder_card)
        layout.addStretch()

    def _setup_menu(self):
        """Set up the menu bar (placeholder)."""
        pass

    def _setup_status_bar(self):
        """Set up the status bar (placeholder)."""
        pass

    def _check_api_key(self):
        """Check for API key (placeholder)."""
        pass

    def _populate_model_dropdown(self):
        """Populate model dropdown (placeholder)."""
        pass

    def _populate_bulk_model_dropdown(self):
        """Populate bulk model dropdown (placeholder)."""
        pass

    def _update_license_status(self):
        """Update license status display."""
        try:
            license_manager = self.controller.license_manager
            status_text = license_manager.get_usage_status_text()
            plan_status = license_manager.get_plan_status_text()

            # Update banner
            full_status = f"{plan_status} | {status_text}"
            is_trial = license_manager.is_trial_plan()
            self.license_banner.update_status(is_trial, full_status)

        except Exception as e:
            self.logger.error(f"Error updating license status: {e}")
            self.license_banner.update_status(True, "License status unavailable")

    def _check_bulk_generation_access(self):
        """Check bulk generation access (placeholder)."""
        pass

    def _show_upgrade_dialog(self):
        """Show upgrade dialog."""
        dialog = ModernUpgradeDialog(
            self,
            "Upgrade to Pro to unlock all features including unlimited image generation, access to all AI models, and bulk generation capabilities.",
            "Pro features"
        )
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._show_license_dialog()

    def _show_license_dialog(self):
        """Show license activation dialog."""
        dialog = ModernLicenseActivationDialog(self, self.controller.license_manager)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Refresh UI after license change
            self._update_license_status()
            self._check_bulk_generation_access()

    def _on_tab_changed(self, index):
        """Handle tab change events (placeholder)."""
        pass

    def _on_prompt_changed(self):
        """Handle prompt text changes (placeholder)."""
        pass

    def _on_provider_changed(self):
        """Handle provider selection changes (placeholder)."""
        pass

    def _on_model_changed(self):
        """Handle model selection changes (placeholder)."""
        pass

    def _on_style_changed(self):
        """Handle style selection changes (placeholder)."""
        pass

    def _on_resolution_type_changed(self):
        """Handle resolution type changes (placeholder)."""
        pass

    def _populate_size_options(self):
        """Populate size options (placeholder)."""
        pass

    def _adjust_width_to_multiple_of_64(self):
        """Adjust width to multiple of 64 (placeholder)."""
        pass

    def _adjust_height_to_multiple_of_64(self):
        """Adjust height to multiple of 64 (placeholder)."""
        pass

    def _generate_image(self):
        """Generate image (placeholder)."""
        QMessageBox.information(self, "Modern UI", "Image generation functionality will be implemented here.")

    def _save_image(self):
        """Save image (placeholder)."""
        QMessageBox.information(self, "Modern UI", "Image save functionality will be implemented here.")

    # Signal handler methods (placeholders)
    def _update_status(self, status):
        """Update status (placeholder)."""
        pass

    def _display_image(self, pixmap):
        """Display generated image (placeholder)."""
        pass

    def _on_generation_started(self):
        """Handle generation started (placeholder)."""
        pass

    def _on_generation_progress(self, progress):
        """Handle generation progress (placeholder)."""
        pass

    def _on_generation_finished(self):
        """Handle generation finished (placeholder)."""
        pass

    def _on_bulk_generation_started(self, total):
        """Handle bulk generation started (placeholder)."""
        pass

    def _on_bulk_generation_progress(self, current, total):
        """Handle bulk generation progress (placeholder)."""
        pass

    def _on_bulk_generation_finished(self, output_folder):
        """Handle bulk generation finished (placeholder)."""
        pass

    def _on_bulk_prompt_processing(self, prompt, index, total):
        """Handle bulk prompt processing (placeholder)."""
        pass

    def _show_error(self, error_message):
        """Show error message (placeholder)."""
        QMessageBox.critical(self, "Error", error_message)

    def _on_usage_updated(self, current, limit):
        """Handle usage updates."""
        self.progress_card.update_usage(current, limit)

    def _on_limit_exceeded(self, message):
        """Handle limit exceeded (placeholder)."""
        pass

    def _on_limit_warning(self, message, remaining):
        """Handle limit warning (placeholder)."""
        pass

    def _on_usage_reset(self):
        """Handle usage reset (placeholder)."""
        pass


# Global exception hook
def exception_hook(exctype, value, tb):
    """Global exception handler to prevent app from crashing."""
    logger = get_logger()
    logger.critical("Unhandled exception", exc_info=(exctype, value, tb))

    # Don't show error dialog for KeyboardInterrupt
    if exctype is not KeyboardInterrupt:
        # Format the traceback
        error_msg = ''.join(traceback.format_exception(exctype, value, tb))

        # Show error dialog
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle("Application Error")
        msg_box.setText("An unexpected error occurred:")
        msg_box.setDetailedText(error_msg)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.exec()

    # Call the original exception hook
    sys.__excepthook__(exctype, value, tb)


# Signal handler for SIGINT (Ctrl+C)
def sigint_handler(*args):
    """Handler for SIGINT signal (Ctrl+C)."""
    logger = get_logger()
    logger.warning("SIGINT received, ignoring to prevent application crash")
    # We don't call sys.exit() here to prevent the app from closing


def main():
    """Main entry point for the modern UI application."""
    # Initialize logger
    logger = get_logger()
    logger.info("Starting BulkAI Modern UI - Unlimited AI Images. One Click Away.")

    # Set up global exception handler
    sys.excepthook = exception_hook
    logger.debug("Global exception handler installed")

    # Install SIGINT handler to prevent Ctrl+C from closing the app
    signal.signal(signal.SIGINT, sigint_handler)
    logger.debug("SIGINT handler installed")

    # Create the application
    app = QApplication(sys.argv)
    logger.debug("QApplication initialized")

    # Use a timer to allow Python to process signals (required for Windows)
    # This ensures our SIGINT handler works properly
    timer = QTimer()
    timer.start(500)  # 500ms interval
    timer.timeout.connect(lambda: None)  # Dummy function

    # Set application name and organization
    app.setApplicationName("BulkAI Modern UI - Unlimited AI Images. One Click Away.")
    app.setOrganizationName("Azanx")

    # Set application icon
    app_icon = QIcon("ui/resources/app-logo.svg")
    app.setWindowIcon(app_icon)
    logger.debug("Application name, organization, and icon set")

    # Create main window
    window = ModernMainWindow()
    window.show()
    logger.info("Modern UI displayed")

    # Start the event loop
    logger.debug("Starting event loop")
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
