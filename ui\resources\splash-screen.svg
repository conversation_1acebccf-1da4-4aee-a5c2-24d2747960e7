<?xml version="1.0" encoding="utf-8" ?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="900" height="500" viewBox="0 0 900 500">
    <defs>
        <!-- Modern gradient background with depth -->
        <linearGradient id="bg_gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#0f0f23;stop-opacity:1" />
            <stop offset="30%" style="stop-color:#1a1a2e;stop-opacity:1" />
            <stop offset="70%" style="stop-color:#16213e;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
        </linearGradient>
        
        <!-- Glass morphism overlay -->
        <linearGradient id="glass_gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1" />
            <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0.05" />
            <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
        </linearGradient>
        
        <!-- Modern title gradient -->
        <linearGradient id="title_gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#64b5f6;stop-opacity:1" />
            <stop offset="30%" style="stop-color:#42a5f5;stop-opacity:1" />
            <stop offset="70%" style="stop-color:#2196f3;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1e88e5;stop-opacity:1" />
        </linearGradient>
        
        <!-- Accent gradient for loading animation -->
        <linearGradient id="accent_gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:1" />
        </linearGradient>
        
        <!-- Subtle shadow filter -->
        <filter id="text_shadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.3"/>
        </filter>
        
        <!-- Glow effect for loading dots -->
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
        
        <!-- Subtle geometric pattern -->
        <pattern id="geometric_pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
            <circle cx="30" cy="30" r="1" fill="#ffffff" opacity="0.03">
                <animate attributeName="opacity" values="0.03;0.08;0.03" dur="4s" repeatCount="indefinite"/>
            </circle>
        </pattern>
    </defs>
    
    <!-- Main background with modern gradient -->
    <rect width="900" height="500" rx="24" ry="24" fill="url(#bg_gradient)"/>
    
    <!-- Geometric pattern overlay -->
    <rect width="900" height="500" rx="24" ry="24" fill="url(#geometric_pattern)"/>
    
    <!-- Glass morphism overlay -->
    <rect x="50" y="50" width="800" height="400" rx="20" ry="20" fill="url(#glass_gradient)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    
    <!-- Floating background elements for depth -->
    <circle cx="150" cy="120" r="80" fill="rgba(100, 181, 246, 0.05)" opacity="0.6">
        <animateTransform attributeName="transform" type="translate" values="0,0; 10,5; 0,0" dur="6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="750" cy="380" r="60" fill="rgba(255, 107, 107, 0.05)" opacity="0.4">
        <animateTransform attributeName="transform" type="translate" values="0,0; -8,3; 0,0" dur="8s" repeatCount="indefinite"/>
    </circle>
    
    <!-- App Name with modern typography -->
    <text x="450" y="180" font-family="'Inter', 'SF Pro Display', 'Segoe UI', sans-serif" font-size="52" font-weight="700" letter-spacing="-0.02em" fill="url(#title_gradient)" text-anchor="middle" filter="url(#text_shadow)">
        Azanx Bulk AI Images
        <animate attributeName="opacity" values="0;1" dur="1s" begin="0.5s" fill="freeze"/>
    </text>
    
    <!-- App Description with improved hierarchy -->
    <text x="450" y="230" font-family="'Inter', 'SF Pro Text', 'Segoe UI', sans-serif" font-size="18" font-weight="400" letter-spacing="0.01em" fill="#b8c5d6" text-anchor="middle" opacity="0.9">
        Generate beautiful AI images in bulk
        <animate attributeName="opacity" values="0;0.9" dur="1s" begin="1s" fill="freeze"/>
    </text>
    
    <!-- Modern loading animation with enhanced effects -->
    <g id="loading-animation" transform="translate(450, 320)">
        <!-- Loading bar background -->
        <rect x="-60" y="-3" width="120" height="6" rx="3" fill="rgba(255,255,255,0.1)"/>
        
        <!-- Animated loading bar -->
        <rect x="-60" y="-3" width="0" height="6" rx="3" fill="url(#accent_gradient)">
            <animate attributeName="width" values="0;120;120" dur="3s" begin="1.5s" repeatCount="indefinite"/>
            <animate attributeName="opacity" values="0;1;1" dur="3s" begin="1.5s" repeatCount="indefinite"/>
        </rect>
        
        <!-- Floating dots with glow effect -->
        <g transform="translate(-30, -25)">
            <circle cx="0" cy="0" r="4" fill="#64b5f6" filter="url(#glow)">
                <animate attributeName="cy" values="0;-8;0" dur="1.2s" repeatCount="indefinite" begin="0s"/>
                <animate attributeName="opacity" values="0.4;1;0.4" dur="1.2s" repeatCount="indefinite" begin="0s"/>
            </circle>
            <circle cx="20" cy="0" r="4" fill="#4ecdc4" filter="url(#glow)">
                <animate attributeName="cy" values="0;-8;0" dur="1.2s" repeatCount="indefinite" begin="0.2s"/>
                <animate attributeName="opacity" values="0.4;1;0.4" dur="1.2s" repeatCount="indefinite" begin="0.2s"/>
            </circle>
            <circle cx="40" cy="0" r="4" fill="#45b7d1" filter="url(#glow)">
                <animate attributeName="cy" values="0;-8;0" dur="1.2s" repeatCount="indefinite" begin="0.4s"/>
                <animate attributeName="opacity" values="0.4;1;0.4" dur="1.2s" repeatCount="indefinite" begin="0.4s"/>
            </circle>
            <circle cx="60" cy="0" r="4" fill="#ff6b6b" filter="url(#glow)">
                <animate attributeName="cy" values="0;-8;0" dur="1.2s" repeatCount="indefinite" begin="0.6s"/>
                <animate attributeName="opacity" values="0.4;1;0.4" dur="1.2s" repeatCount="indefinite" begin="0.6s"/>
            </circle>
        </g>
    </g>
    
    <!-- Made with love in Pakistan with elegant styling -->
    <text x="450" y="450" font-family="'Inter', 'SF Pro Text', 'Segoe UI', sans-serif" font-size="13" font-weight="300" font-style="italic" letter-spacing="0.02em" fill="#7a8a9a" text-anchor="middle" opacity="0.8">
        Made with love in Pakistan
        <animate attributeName="opacity" values="0;0.8" dur="1s" begin="2s" fill="freeze"/>
    </text>
</svg>
