<?php
require_once 'config.php';
checkAuth();

$pdo = getDatabase();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                try {
                    $license_key = generateLicenseKey();
                    $expiry_date = null;

                    // Calculate expiry date based on plan type
                    if ($_POST['plan_id']) {
                        $stmt = $pdo->prepare("SELECT plan_type FROM license_plans WHERE id = ?");
                        $stmt->execute([$_POST['plan_id']]);
                        $plan = $stmt->fetch();

                        if ($plan) {
                            switch ($plan['plan_type']) {
                                case 'monthly':
                                    $expiry_date = date('Y-m-d H:i:s', strtotime('+1 month'));
                                    break;
                                case 'yearly':
                                    $expiry_date = date('Y-m-d H:i:s', strtotime('+1 year'));
                                    break;
                                case 'lifetime':
                                    $expiry_date = null; // Never expires
                                    break;
                            }
                        }
                    }

                    $stmt = $pdo->prepare("INSERT INTO licenses (license_key, customer_id, plan_id, expiry_date, max_activations, notes) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $license_key,
                        $_POST['customer_id'],
                        $_POST['plan_id'],
                        $expiry_date,
                        $_POST['max_activations'] ?? 1,
                        $_POST['notes']
                    ]);
                    $message = 'License created successfully! License Key: ' . $license_key;
                } catch (PDOException $e) {
                    $error = 'Error creating license: ' . $e->getMessage();
                }
                break;

            case 'update':
                try {
                    $stmt = $pdo->prepare("UPDATE licenses SET customer_id = ?, plan_id = ?, expiry_date = ?, max_activations = ?, status = ?, notes = ?, updated_date = CURRENT_TIMESTAMP WHERE id = ?");
                    $stmt->execute([
                        $_POST['customer_id'],
                        $_POST['plan_id'],
                        $_POST['expiry_date'] ?: null,
                        $_POST['max_activations'],
                        $_POST['status'],
                        $_POST['notes'],
                        $_POST['id']
                    ]);
                    $message = 'License updated successfully!';
                } catch (PDOException $e) {
                    $error = 'Error updating license: ' . $e->getMessage();
                }
                break;

            case 'activate':
                try {
                    $stmt = $pdo->prepare("UPDATE licenses SET status = 'active', activation_date = CURRENT_TIMESTAMP WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'License activated successfully!';
                } catch (PDOException $e) {
                    $error = 'Error activating license: ' . $e->getMessage();
                }
                break;

            case 'deactivate':
                try {
                    $stmt = $pdo->prepare("UPDATE licenses SET status = 'inactive' WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'License deactivated successfully!';
                } catch (PDOException $e) {
                    $error = 'Error deactivating license: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get license for editing
$edit_license = null;
if (isset($_GET['edit'])) {
    $stmt = $pdo->prepare("
        SELECT l.*, c.name as customer_name, p.plan_name
        FROM licenses l
        JOIN customers c ON l.customer_id = c.id
        JOIN license_plans p ON l.plan_id = p.id
        WHERE l.id = ?
    ");
    $stmt->execute([$_GET['edit']]);
    $edit_license = $stmt->fetch();
}

// Get all customers for dropdown
$stmt = $pdo->query("SELECT id, name, email FROM customers WHERE status = 'active' ORDER BY name");
$customers = $stmt->fetchAll();

// Get all license plans for dropdown
$stmt = $pdo->query("SELECT id, plan_name, plan_type FROM license_plans WHERE is_active = 1 ORDER BY plan_name");
$plans = $stmt->fetchAll();

// Get all licenses with customer and plan info
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$plan_filter = $_GET['plan'] ?? '';

$sql = "
    SELECT l.*, c.name as customer_name, c.email, p.plan_name, p.plan_type
    FROM licenses l
    JOIN customers c ON l.customer_id = c.id
    JOIN license_plans p ON l.plan_id = p.id
    WHERE 1=1
";
$params = [];

if ($search) {
    $sql .= " AND (l.license_key LIKE ? OR c.name LIKE ? OR c.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $sql .= " AND l.status = ?";
    $params[] = $status_filter;
}

if ($plan_filter) {
    $sql .= " AND l.plan_id = ?";
    $params[] = $plan_filter;
}

$sql .= " ORDER BY l.created_date DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$licenses = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Licenses - BulkAI License Admin</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="dashboard">
    <div class="header">
        <h1>BulkAI License Admin</h1>
        <nav class="nav">
            <a href="dashboard.php">Dashboard</a>
            <a href="customers.php">Customers</a>
            <a href="licenses.php" class="active">Licenses</a>
            <a href="usage.php">Usage</a>
            <a href="index.php?logout=1">Logout</a>
        </nav>
    </div>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <!-- License Form -->
        <div class="table-container">
            <div class="table-header">
                <h2><?php echo $edit_license ? 'Edit License' : 'Create New License'; ?></h2>
            </div>
            <div style="padding: 20px;">
                <form method="POST">
                    <input type="hidden" name="action" value="<?php echo $edit_license ? 'update' : 'create'; ?>">
                    <?php if ($edit_license): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_license['id']; ?>">
                    <?php endif; ?>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label for="customer_id">Customer *</label>
                            <select id="customer_id" name="customer_id" required>
                                <option value="">Select Customer</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>"
                                            <?php echo ($edit_license && $edit_license['customer_id'] == $customer['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($customer['name'] . ' (' . $customer['email'] . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="plan_id">License Plan *</label>
                            <select id="plan_id" name="plan_id" required>
                                <option value="">Select Plan</option>
                                <?php foreach ($plans as $plan): ?>
                                    <option value="<?php echo $plan['id']; ?>"
                                            <?php echo ($edit_license && $edit_license['plan_id'] == $plan['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($plan['plan_name'] . ' (' . ucfirst($plan['plan_type']) . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="max_activations">Max Device Activations</label>
                            <input type="number" id="max_activations" name="max_activations" min="1" max="10"
                                   value="<?php echo htmlspecialchars($edit_license['max_activations'] ?? '1'); ?>">
                        </div>

                        <?php if ($edit_license): ?>
                        <div class="form-group">
                            <label for="expiry_date">Expiry Date</label>
                            <input type="datetime-local" id="expiry_date" name="expiry_date"
                                   value="<?php echo $edit_license['expiry_date'] ? date('Y-m-d\TH:i', strtotime($edit_license['expiry_date'])) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status">
                                <option value="active" <?php echo ($edit_license['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo ($edit_license['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                <option value="expired" <?php echo ($edit_license['status'] === 'expired') ? 'selected' : ''; ?>>Expired</option>
                                <option value="suspended" <?php echo ($edit_license['status'] === 'suspended') ? 'selected' : ''; ?>>Suspended</option>
                                <option value="revoked" <?php echo ($edit_license['status'] === 'revoked') ? 'selected' : ''; ?>>Revoked</option>
                            </select>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="Optional notes about this license..."><?php echo htmlspecialchars($edit_license['notes'] ?? ''); ?></textarea>
                    </div>

                    <div style="margin-top: 20px;">
                        <button type="submit" class="btn btn-primary">
                            <?php echo $edit_license ? 'Update License' : 'Create License'; ?>
                        </button>
                        <?php if ($edit_license): ?>
                            <a href="licenses.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="table-container">
            <div class="table-header">
                <h2>All Licenses</h2>
            </div>
            <div style="padding: 20px; border-bottom: 1px solid #dee2e6;">
                <form method="GET" style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                    <div class="form-group" style="margin-bottom: 0; flex: 1; min-width: 200px;">
                        <label for="search">Search</label>
                        <input type="text" id="search" name="search" placeholder="License key, customer name, or email..."
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="active" <?php echo ($status_filter === 'active') ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($status_filter === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                            <option value="expired" <?php echo ($status_filter === 'expired') ? 'selected' : ''; ?>>Expired</option>
                            <option value="suspended" <?php echo ($status_filter === 'suspended') ? 'selected' : ''; ?>>Suspended</option>
                            <option value="revoked" <?php echo ($status_filter === 'revoked') ? 'selected' : ''; ?>>Revoked</option>
                        </select>
                    </div>
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="plan">Plan</label>
                        <select id="plan" name="plan">
                            <option value="">All Plans</option>
                            <?php foreach ($plans as $plan): ?>
                                <option value="<?php echo $plan['id']; ?>" <?php echo ($plan_filter == $plan['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($plan['plan_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Search</button>
                    <a href="licenses.php" class="btn btn-secondary">Clear</a>
                </form>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>License Key</th>
                        <th>Customer</th>
                        <th>Plan</th>
                        <th>Status</th>
                        <th>Activations</th>
                        <th>Created</th>
                        <th>Expires</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($licenses as $license): ?>
                    <tr>
                        <td><code style="font-size: 11px;"><?php echo htmlspecialchars($license['license_key']); ?></code></td>
                        <td>
                            <?php echo htmlspecialchars($license['customer_name']); ?><br>
                            <small><?php echo htmlspecialchars($license['email']); ?></small>
                        </td>
                        <td><?php echo htmlspecialchars($license['plan_name']); ?></td>
                        <td><?php echo getStatusBadge($license['status']); ?></td>
                        <td><?php echo $license['current_activations'] . '/' . $license['max_activations']; ?></td>
                        <td><?php echo formatDate($license['created_date']); ?></td>
                        <td><?php echo $license['expiry_date'] ? formatDate($license['expiry_date']) : 'Never'; ?></td>
                        <td>
                            <a href="licenses.php?edit=<?php echo $license['id']; ?>" class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">Edit</a>

                            <?php if ($license['status'] === 'inactive'): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="activate">
                                    <input type="hidden" name="id" value="<?php echo $license['id']; ?>">
                                    <button type="submit" class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">Activate</button>
                                </form>
                            <?php elseif ($license['status'] === 'active'): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="deactivate">
                                    <input type="hidden" name="id" value="<?php echo $license['id']; ?>">
                                    <button type="submit" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Deactivate</button>
                                </form>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>

                    <?php if (empty($licenses)): ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                            No licenses found.
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
