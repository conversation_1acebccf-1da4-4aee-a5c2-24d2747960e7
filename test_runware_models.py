#!/usr/bin/env python3
"""
Test script to verify Runware AI models are being loaded correctly.
This helps debug the "no models available" issue in the executable.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager
from api.runware_client import RunwareAIClient
from logger import get_logger

def test_config_manager():
    """Test the config manager's ability to load Runware models."""
    print("=" * 60)
    print("Testing ConfigManager - Runware Models")
    print("=" * 60)
    
    config_manager = ConfigManager()
    
    # Test new format (api_providers)
    print("\n1. Testing new api_providers format:")
    try:
        models = config_manager.get_provider_models("runware_ai")
        print(f"   Found {len(models)} models from api_providers.runware_ai")
        for i, model in enumerate(models[:5]):  # Show first 5 models
            print(f"   Model {i+1}: {model.get('name', 'Unknown')} ({model.get('id', 'Unknown ID')})")
        if len(models) > 5:
            print(f"   ... and {len(models) - 5} more models")
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test old format (runware_models)
    print("\n2. Testing old runware_models format:")
    try:
        models = config_manager.get_runware_models()
        print(f"   Found {len(models)} models from runware_models")
        for i, model in enumerate(models[:3]):  # Show first 3 models
            print(f"   Model {i+1}: {model.get('name', 'Unknown')} ({model.get('id', 'Unknown ID')})")
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test all providers
    print("\n3. Testing all API providers:")
    try:
        providers = config_manager.get_api_providers()
        print(f"   Found {len(providers)} providers:")
        for provider_key, provider_data in providers.items():
            models_count = len(provider_data.get('models', []))
            print(f"   - {provider_key}: {models_count} models")
    except Exception as e:
        print(f"   ERROR: {e}")

def test_runware_client():
    """Test the Runware client's ability to load models."""
    print("\n" + "=" * 60)
    print("Testing RunwareAIClient - Model Loading")
    print("=" * 60)
    
    # We need to provide a dummy API key to test the client
    # The client will fail to initialize without one
    try:
        # Try to create the client with a dummy key
        config_manager = ConfigManager()
        
        # Set a dummy API key temporarily
        original_key = config_manager.get_api_key("runware_ai")
        if not original_key:
            print("\n   No Runware API key found in config. Using dummy key for testing...")
            # We'll catch the exception when it tries to validate the key
        
        try:
            client = RunwareAIClient(api_key="dummy_key_for_testing", config_manager=config_manager)
            models = client.get_available_models()
            print(f"\n   RunwareAIClient loaded {len(models)} models:")
            for i, model in enumerate(models[:5]):  # Show first 5 models
                print(f"   Model {i+1}: {model.get('name', 'Unknown')} ({model.get('id', 'Unknown ID')})")
            if len(models) > 5:
                print(f"   ... and {len(models) - 5} more models")
        except ValueError as e:
            if "API key must be provided" in str(e):
                print(f"\n   Expected error (no API key): {e}")
                print("   This is normal - the client requires a valid API key")
            else:
                print(f"\n   Unexpected error: {e}")
        except Exception as e:
            print(f"\n   ERROR creating RunwareAIClient: {e}")
            
    except Exception as e:
        print(f"\n   ERROR: {e}")

def test_config_file():
    """Test if the config file exists and is readable."""
    print("\n" + "=" * 60)
    print("Testing Config File")
    print("=" * 60)
    
    config_manager = ConfigManager()
    print(f"\n   Config file path: {config_manager.config_path}")
    print(f"   Config file exists: {config_manager.config_path.exists()}")
    
    if config_manager.config_path.exists():
        try:
            import json
            with open(config_manager.config_path, 'r') as f:
                config_data = json.load(f)
            
            print(f"   Config file is valid JSON: True")
            print(f"   Has 'api_providers' key: {'api_providers' in config_data}")
            print(f"   Has 'runware_models' key: {'runware_models' in config_data}")
            
            if 'api_providers' in config_data:
                providers = config_data['api_providers']
                print(f"   API providers found: {list(providers.keys())}")
                if 'runware_ai' in providers:
                    runware_models = providers['runware_ai'].get('models', [])
                    print(f"   Runware AI models in config: {len(runware_models)}")
                else:
                    print("   No 'runware_ai' provider found in api_providers")
            
        except Exception as e:
            print(f"   ERROR reading config file: {e}")

def main():
    """Main test function."""
    print("BulkAI Runware Models Test")
    print("This script tests if Runware AI models are being loaded correctly.")
    print("Use this to debug the 'no models available' issue in the executable.")
    
    # Test config file
    test_config_file()
    
    # Test config manager
    test_config_manager()
    
    # Test Runware client
    test_runware_client()
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("=" * 60)
    print("\nIf you see models listed above, the configuration is working correctly.")
    print("If no models are found, check:")
    print("1. The config.json file exists and has the correct structure")
    print("2. The 'api_providers.runware_ai.models' section has model definitions")
    print("3. The executable includes the config.json file")

if __name__ == "__main__":
    main()
