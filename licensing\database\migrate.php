<?php
/**
 * Database migration script for BulkAI licensing system
 * This script ensures the database schema is up to date
 */

require_once '../config/database.php';

function runMigration() {
    try {
        $pdo = getDatabaseConnection();
        
        echo "Starting database migration...\n";
        
        // Check if deactivation_date column exists
        $stmt = $pdo->query("PRAGMA table_info(device_activations)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasDeactivationDate = false;
        foreach ($columns as $column) {
            if ($column['name'] === 'deactivation_date') {
                $hasDeactivationDate = true;
                break;
            }
        }
        
        if (!$hasDeactivationDate) {
            echo "Adding deactivation_date column to device_activations table...\n";
            $pdo->exec("ALTER TABLE device_activations ADD COLUMN deactivation_date DATETIME");
            echo "✅ deactivation_date column added successfully\n";
        } else {
            echo "✅ deactivation_date column already exists\n";
        }
        
        // Verify the column exists now
        $stmt = $pdo->query("PRAGMA table_info(device_activations)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\nCurrent device_activations table schema:\n";
        foreach ($columns as $column) {
            echo "- {$column['name']} ({$column['type']})\n";
        }
        
        // Test the deactivation functionality
        echo "\nTesting deactivation functionality...\n";
        
        // Try to run a test query
        $stmt = $pdo->prepare("SELECT id, status, deactivation_date FROM device_activations LIMIT 1");
        $stmt->execute();
        
        echo "✅ Deactivation functionality test passed\n";
        
        echo "\nMigration completed successfully!\n";
        
    } catch (PDOException $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
        return false;
    } catch (Exception $e) {
        echo "❌ Migration error: " . $e->getMessage() . "\n";
        return false;
    }
    
    return true;
}

// Run the migration
if (php_sapi_name() === 'cli') {
    // Running from command line
    $success = runMigration();
    exit($success ? 0 : 1);
} else {
    // Running from web browser
    header('Content-Type: text/plain');
    runMigration();
}
?>
