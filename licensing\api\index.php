<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../admin/config.php';

// Simple API key authentication (optional)
$api_key = $_SERVER['HTTP_X_API_KEY'] ?? $_GET['api_key'] ?? null;
$valid_api_keys = ['bulkai-api-2025', 'desktop-app-key']; // Change these!

// For now, we'll allow requests without API key for simplicity
// Uncomment below to enforce API key authentication
/*
if (!$api_key || !in_array($api_key, $valid_api_keys)) {
    http_response_code(401);
    echo json_encode(['error' => 'Invalid or missing API key']);
    exit;
}
*/

$pdo = getDatabase();

// Get the request path
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove common path prefixes to handle different deployment scenarios
$path = str_replace('/licensing/api', '', $path);
$path = str_replace('/index.php', '', $path);

// Handle both direct calls and rewritten URLs
if (empty($path) || $path === '/' || $path === '/index.php') {
    // Check if this is a direct call with action parameter
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    if ($action) {
        $path = '/' . $action;
    }
}

// Log the path for debugging (remove in production)
error_log("API Request Path: " . $path);

// Route the request
switch ($path) {
    case '/verify-license':
        handleVerifyLicense($pdo);
        break;
    case '/license-info':
        handleLicenseInfo($pdo);
        break;
    case '/usage-tracking':
        handleUsageTracking($pdo);
        break;
    case '/activate-license':
        handleActivateLicense($pdo);
        break;
    case '/deactivate-license':
        handleDeactivateLicense($pdo);
        break;
    default:
        http_response_code(404);
        echo json_encode([
            'error' => 'Endpoint not found',
            'requested_path' => $path,
            'available_endpoints' => [
                '/verify-license',
                '/license-info',
                '/usage-tracking',
                '/activate-license',
                '/deactivate-license'
            ]
        ]);
        break;
}

function handleVerifyLicense($pdo) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $license_key = $input['license_key'] ?? '';
    $device_id = $input['device_id'] ?? '';

    if (!$license_key) {
        http_response_code(400);
        echo json_encode(['error' => 'License key is required']);
        return;
    }

    try {
        // Get license with customer and plan info
        $stmt = $pdo->prepare("
            SELECT l.*, c.name as customer_name, c.email, p.plan_name, p.plan_type, p.daily_image_limit, p.features
            FROM licenses l
            JOIN customers c ON l.customer_id = c.id
            JOIN license_plans p ON l.plan_id = p.id
            WHERE l.license_key = ?
        ");
        $stmt->execute([$license_key]);
        $license = $stmt->fetch();

        if (!$license) {
            echo json_encode([
                'valid' => false,
                'error' => 'License not found'
            ]);
            return;
        }

        // Check if license is active
        if ($license['status'] !== 'active') {
            echo json_encode([
                'valid' => false,
                'error' => 'License is not active',
                'status' => $license['status']
            ]);
            return;
        }

        // Check if license has expired
        if ($license['expiry_date'] && strtotime($license['expiry_date']) < time()) {
            // Update license status to expired
            $stmt = $pdo->prepare("UPDATE licenses SET status = 'expired' WHERE id = ?");
            $stmt->execute([$license['id']]);

            echo json_encode([
                'valid' => false,
                'error' => 'License has expired',
                'expiry_date' => $license['expiry_date']
            ]);
            return;
        }

        // Check device activation if device_id is provided
        $device_activated = false;
        if ($device_id) {
            $stmt = $pdo->prepare("SELECT * FROM device_activations WHERE license_id = ? AND device_id = ? AND status = 'active'");
            $stmt->execute([$license['id'], $device_id]);
            $device_activated = $stmt->fetch() !== false;
        }

        // Get today's usage for this license
        $stmt = $pdo->prepare("
            SELECT SUM(images_generated) as today_usage
            FROM usage_tracking
            WHERE license_id = ? AND usage_date = DATE('now')
        ");
        $stmt->execute([$license['id']]);
        $usage = $stmt->fetch();
        $today_usage = $usage['today_usage'] ?? 0;

        // Check daily limit for non-unlimited plans
        $daily_limit_exceeded = false;
        if ($license['daily_image_limit'] > 0 && $today_usage >= $license['daily_image_limit']) {
            $daily_limit_exceeded = true;
        }

        echo json_encode([
            'valid' => true,
            'license' => [
                'license_key' => $license['license_key'],
                'customer_name' => $license['customer_name'],
                'plan_name' => $license['plan_name'],
                'plan_type' => $license['plan_type'],
                'daily_image_limit' => $license['daily_image_limit'],
                'features' => json_decode($license['features'], true),
                'expiry_date' => $license['expiry_date'],
                'max_activations' => $license['max_activations'],
                'current_activations' => $license['current_activations']
            ],
            'usage' => [
                'today_usage' => $today_usage,
                'daily_limit_exceeded' => $daily_limit_exceeded
            ],
            'device' => [
                'activated' => $device_activated,
                'device_id' => $device_id
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
}

function handleLicenseInfo($pdo) {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $license_key = $_GET['license_key'] ?? '';

    if (!$license_key) {
        http_response_code(400);
        echo json_encode(['error' => 'License key is required']);
        return;
    }

    try {
        $stmt = $pdo->prepare("
            SELECT l.*, c.name as customer_name, c.email, p.plan_name, p.plan_type, p.daily_image_limit, p.features
            FROM licenses l
            JOIN customers c ON l.customer_id = c.id
            JOIN license_plans p ON l.plan_id = p.id
            WHERE l.license_key = ?
        ");
        $stmt->execute([$license_key]);
        $license = $stmt->fetch();

        if (!$license) {
            http_response_code(404);
            echo json_encode(['error' => 'License not found']);
            return;
        }

        echo json_encode([
            'license_key' => $license['license_key'],
            'customer_name' => $license['customer_name'],
            'plan_name' => $license['plan_name'],
            'plan_type' => $license['plan_type'],
            'status' => $license['status'],
            'daily_image_limit' => $license['daily_image_limit'],
            'features' => json_decode($license['features'], true),
            'activation_date' => $license['activation_date'],
            'expiry_date' => $license['expiry_date'],
            'max_activations' => $license['max_activations'],
            'current_activations' => $license['current_activations']
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
}

function handleUsageTracking($pdo) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $license_key = $input['license_key'] ?? '';
    $device_id = $input['device_id'] ?? '';
    $images_generated = $input['images_generated'] ?? 0;
    $api_calls = $input['api_calls'] ?? 0;
    $provider_used = $input['provider_used'] ?? '';
    $model_used = $input['model_used'] ?? '';

    if (!$license_key || !$device_id) {
        http_response_code(400);
        echo json_encode(['error' => 'License key and device ID are required']);
        return;
    }

    try {
        // Get license ID
        $stmt = $pdo->prepare("SELECT id FROM licenses WHERE license_key = ?");
        $stmt->execute([$license_key]);
        $license = $stmt->fetch();

        if (!$license) {
            http_response_code(404);
            echo json_encode(['error' => 'License not found']);
            return;
        }

        // Insert or update usage tracking
        $stmt = $pdo->prepare("
            INSERT INTO usage_tracking (license_id, device_id, usage_date, images_generated, api_calls, provider_used, model_used)
            VALUES (?, ?, DATE('now'), ?, ?, ?, ?)
            ON CONFLICT(license_id, device_id, usage_date) DO UPDATE SET
                images_generated = images_generated + ?,
                api_calls = api_calls + ?,
                provider_used = ?,
                model_used = ?
        ");
        $stmt->execute([
            $license['id'], $device_id, $images_generated, $api_calls, $provider_used, $model_used,
            $images_generated, $api_calls, $provider_used, $model_used
        ]);

        echo json_encode(['success' => true, 'message' => 'Usage tracked successfully']);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
}

function handleActivateLicense($pdo) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $license_key = $input['license_key'] ?? '';
    $device_id = $input['device_id'] ?? '';
    $device_name = $input['device_name'] ?? '';
    $device_info = $input['device_info'] ?? '';

    if (!$license_key || !$device_id) {
        http_response_code(400);
        echo json_encode(['error' => 'License key and device ID are required']);
        return;
    }

    try {
        // Get license info
        $stmt = $pdo->prepare("SELECT * FROM licenses WHERE license_key = ?");
        $stmt->execute([$license_key]);
        $license = $stmt->fetch();

        if (!$license) {
            echo json_encode([
                'success' => false,
                'error' => 'License not found'
            ]);
            return;
        }

        if ($license['status'] !== 'active') {
            echo json_encode([
                'success' => false,
                'error' => 'License is not active'
            ]);
            return;
        }

        // Check if device is already activated
        $stmt = $pdo->prepare("SELECT * FROM device_activations WHERE license_id = ? AND device_id = ?");
        $stmt->execute([$license['id'], $device_id]);
        $existing_activation = $stmt->fetch();

        if ($existing_activation) {
            // Update last seen
            $stmt = $pdo->prepare("UPDATE device_activations SET last_seen = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$existing_activation['id']]);

            echo json_encode([
                'success' => true,
                'message' => 'Device already activated',
                'activation_date' => $existing_activation['activation_date']
            ]);
            return;
        }

        // Check activation limit
        if ($license['current_activations'] >= $license['max_activations']) {
            echo json_encode([
                'success' => false,
                'error' => 'Maximum device activations reached',
                'max_activations' => $license['max_activations'],
                'current_activations' => $license['current_activations']
            ]);
            return;
        }

        // Activate device
        $stmt = $pdo->prepare("
            INSERT INTO device_activations (license_id, device_id, device_name, device_info)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$license['id'], $device_id, $device_name, $device_info]);

        // Update license activation count
        $stmt = $pdo->prepare("UPDATE licenses SET current_activations = current_activations + 1 WHERE id = ?");
        $stmt->execute([$license['id']]);

        echo json_encode([
            'success' => true,
            'message' => 'Device activated successfully',
            'activation_date' => date('Y-m-d H:i:s')
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
}

function handleDeactivateLicense($pdo) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $license_key = $input['license_key'] ?? '';
    $device_id = $input['device_id'] ?? '';

    if (!$license_key || !$device_id) {
        http_response_code(400);
        echo json_encode(['error' => 'License key and device ID are required']);
        return;
    }

    try {
        // Start transaction
        $pdo->beginTransaction();

        // Get license info
        $stmt = $pdo->prepare("SELECT * FROM licenses WHERE license_key = ?");
        $stmt->execute([$license_key]);
        $license = $stmt->fetch();

        if (!$license) {
            $pdo->rollBack();
            echo json_encode([
                'success' => false,
                'error' => 'License not found'
            ]);
            return;
        }

        // Check if device is activated
        $stmt = $pdo->prepare("SELECT * FROM device_activations WHERE license_id = ? AND device_id = ? AND status = 'active'");
        $stmt->execute([$license['id'], $device_id]);
        $activation = $stmt->fetch();

        if (!$activation) {
            $pdo->rollBack();
            echo json_encode([
                'success' => false,
                'error' => 'Device is not activated for this license'
            ]);
            return;
        }

        // Check if deactivation_date column exists, if not add it
        try {
            $stmt = $pdo->prepare("SELECT deactivation_date FROM device_activations LIMIT 1");
            $stmt->execute();
        } catch (PDOException $e) {
            // Column doesn't exist, add it
            $pdo->exec("ALTER TABLE device_activations ADD COLUMN deactivation_date DATETIME");
        }

        // Deactivate device
        $deactivation_date = date('Y-m-d H:i:s');
        $stmt = $pdo->prepare("UPDATE device_activations SET status = 'deactivated', deactivation_date = ? WHERE id = ?");
        $stmt->execute([$deactivation_date, $activation['id']]);

        // Update license activation count (ensure it doesn't go below 0)
        $stmt = $pdo->prepare("UPDATE licenses SET current_activations = CASE WHEN current_activations > 0 THEN current_activations - 1 ELSE 0 END WHERE id = ?");
        $stmt->execute([$license['id']]);

        // Commit transaction
        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Device deactivated successfully',
            'deactivation_date' => $deactivation_date
        ]);

    } catch (PDOException $e) {
        // Rollback transaction on database error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        // Log the actual error for debugging
        error_log("License deactivation database error: " . $e->getMessage());

        http_response_code(500);
        echo json_encode([
            'error' => 'Database error occurred',
            'debug_info' => $e->getMessage() // Remove this in production
        ]);
    } catch (Exception $e) {
        // Rollback transaction on any other error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        // Log the actual error for debugging
        error_log("License deactivation error: " . $e->getMessage());

        http_response_code(500);
        echo json_encode([
            'error' => 'Internal server error',
            'debug_info' => $e->getMessage() // Remove this in production
        ]);
    }
}