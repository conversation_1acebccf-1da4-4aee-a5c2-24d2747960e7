-- Migration script to add deactivation_date column if it doesn't exist
-- This ensures backward compatibility with existing databases

-- Check if the column exists and add it if it doesn't
-- SQLite doesn't have IF NOT EXISTS for ALTER TABLE, so we use a different approach

-- First, check if we need to add the column by trying to select from it
-- If this fails, the column doesn't exist and we need to add it

-- For SQLite, we need to use a more complex approach
-- Create a backup table, recreate the original with new schema, copy data back

-- Check current schema
PRAGMA table_info(device_activations);

-- Add deactivation_date column if it doesn't exist
-- This will fail silently if the column already exists
ALTER TABLE device_activations ADD COLUMN deactivation_date DATETIME;

-- Update the status constraint to include 'deactivated' if needed
-- Note: SQLite doesn't support modifying constraints, so this is handled in the main schema

-- Verify the column was added
PRAGMA table_info(device_activations);
