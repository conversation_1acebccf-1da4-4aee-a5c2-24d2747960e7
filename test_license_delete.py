#!/usr/bin/env python3
"""
Test script for license deletion/deactivation functionality.
"""

import requests
import json
import platform
import hashlib

def test_license_deactivation():
    """Test the license deactivation endpoint."""

    # Configuration
    license_server_url = "https://bulkimages.azanx.com/licensing/api"
    test_license_key = "BULKAI-67BD2EA2-CE1423CF-40039D04"  # Use the working test key

    # Generate a test device ID
    hardware_string = f"{platform.machine()}-{platform.processor()}-{platform.system()}-{platform.node()}"
    device_id = hashlib.sha256(hardware_string.encode()).hexdigest()[:16]

    print(f"Testing license deactivation endpoint...")
    print(f"Server URL: {license_server_url}")
    print(f"Device ID: {device_id}")
    print(f"License Key: {test_license_key}")
    print("=" * 60)

    # Test 1: First ensure the license is activated
    print("\n1. Ensuring License is Activated")
    print("-" * 40)
    try:
        device_info = {
            'system': platform.system(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version()
        }

        response = requests.post(
            f"{license_server_url}/?action=activate-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id,
                'device_name': platform.node(),
                'device_info': json.dumps(device_info)
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )

        print(f"Activation Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Activation Success: {result.get('success', False)}")
            print(f"Activation Message: {result.get('message', 'No message')}")
        else:
            print(f"Activation Error: {response.text}")

    except Exception as e:
        print(f"Activation Error: {e}")

    # Test 2: Verify the license is active
    print("\n2. Verifying License Status")
    print("-" * 40)
    try:
        response = requests.post(
            f"{license_server_url}/?action=verify-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )

        print(f"Verification Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"License Valid: {result.get('valid', False)}")
            device_info = result.get('device', {})
            print(f"Device Activated: {device_info.get('activated', False)}")
        else:
            print(f"Verification Error: {response.text}")

    except Exception as e:
        print(f"Verification Error: {e}")

    # Test 3: Deactivate the license
    print("\n3. Testing License Deactivation")
    print("-" * 40)
    try:
        response = requests.post(
            f"{license_server_url}/?action=deactivate-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )

        print(f"Deactivation Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Deactivation Success: {result.get('success', False)}")
            print(f"Deactivation Message: {result.get('message', 'No message')}")
            if 'deactivation_date' in result:
                print(f"Deactivation Date: {result['deactivation_date']}")
        else:
            try:
                error_response = response.json()
                print(f"Deactivation Error: {error_response.get('error', 'Unknown error')}")
                if 'debug_info' in error_response:
                    print(f"Debug Info: {error_response['debug_info']}")
            except:
                print(f"Deactivation Error: {response.text}")

    except Exception as e:
        print(f"Deactivation Error: {e}")

    # Test 4: Verify the license is now deactivated
    print("\n4. Verifying License is Deactivated")
    print("-" * 40)
    try:
        response = requests.post(
            f"{license_server_url}/?action=verify-license",
            json={
                'license_key': test_license_key,
                'device_id': device_id
            },
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )

        print(f"Verification Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"License Valid: {result.get('valid', False)}")
            device_info = result.get('device', {})
            print(f"Device Activated: {device_info.get('activated', False)}")
        else:
            print(f"Verification Error: {response.text}")

    except Exception as e:
        print(f"Verification Error: {e}")

    print("\n" + "=" * 60)
    print("License deactivation test completed!")

if __name__ == "__main__":
    test_license_deactivation()
