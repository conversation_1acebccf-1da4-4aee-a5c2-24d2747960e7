"""
Real-time image generation usage tracking for BulkAI desktop app.

This module provides real-time tracking of image generation with persistent storage,
daily limit enforcement, and real-time UI updates.
"""

import json
import os
import datetime
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSignal
from logger import get_logger


class UsageTracker(QObject):
    """Tracks image generation usage with real-time updates and persistent storage."""
    
    # Signals for real-time UI updates
    usage_updated = pyqtSignal(int, int)  # current_count, daily_limit
    limit_exceeded = pyqtSignal(str)  # error_message
    limit_warning = pyqtSignal(str, int)  # warning_message, remaining_count
    reset_occurred = pyqtSignal()  # daily reset happened
    
    def __init__(self, config_manager, license_manager):
        """Initialize the usage tracker.
        
        Args:
            config_manager: ConfigManager instance
            license_manager: LicenseManager instance
        """
        super().__init__()
        self.config_manager = config_manager
        self.license_manager = license_manager
        self.logger = get_logger()
        
        # Storage file for usage data
        self.usage_file = Path("usage_data.json")
        
        # Load existing usage data
        self.usage_data = self._load_usage_data()
        
        # Check if we need to reset daily counts
        self._check_daily_reset()
        
        self.logger.debug("UsageTracker initialized")
    
    def _load_usage_data(self):
        """Load usage data from persistent storage.
        
        Returns:
            dict: Usage data with daily counts and timestamps
        """
        default_data = {
            "daily_count": 0,
            "last_reset_date": datetime.date.today().isoformat(),
            "first_generation_time": None,
            "total_generated": 0,
            "session_count": 0
        }
        
        if not self.usage_file.exists():
            self.logger.debug("Usage data file not found, creating default")
            self._save_usage_data(default_data)
            return default_data
        
        try:
            with open(self.usage_file, 'r') as f:
                data = json.load(f)
                self.logger.debug(f"Loaded usage data: {data}")
                return data
        except (json.JSONDecodeError, IOError) as e:
            self.logger.error(f"Error loading usage data: {e}")
            self.logger.warning("Creating new default usage data")
            self._save_usage_data(default_data)
            return default_data
    
    def _save_usage_data(self, data=None):
        """Save usage data to persistent storage.
        
        Args:
            data (dict, optional): Data to save. If None, saves current usage_data.
        """
        if data is None:
            data = self.usage_data
        
        try:
            with open(self.usage_file, 'w') as f:
                json.dump(data, f, indent=2)
                self.logger.debug(f"Saved usage data: {data}")
        except IOError as e:
            self.logger.error(f"Error saving usage data: {e}")
    
    def _check_daily_reset(self):
        """Check if daily counts need to be reset."""
        today = datetime.date.today().isoformat()
        last_reset = self.usage_data.get("last_reset_date")
        
        if last_reset != today:
            self.logger.info(f"Daily reset triggered: {last_reset} -> {today}")
            self._reset_daily_count()
            self.reset_occurred.emit()
    
    def _reset_daily_count(self):
        """Reset daily usage count."""
        today = datetime.date.today().isoformat()
        self.usage_data["daily_count"] = 0
        self.usage_data["last_reset_date"] = today
        self.usage_data["session_count"] = 0
        self._save_usage_data()
        self.logger.info("Daily usage count reset")
    
    def get_daily_count(self):
        """Get current daily image generation count.
        
        Returns:
            int: Number of images generated today
        """
        self._check_daily_reset()
        return self.usage_data.get("daily_count", 0)
    
    def get_daily_limit(self):
        """Get daily generation limit based on license.
        
        Returns:
            int: Daily limit (-1 for unlimited, positive number for limit)
        """
        return self.license_manager.get_daily_usage_limit()
    
    def get_remaining_count(self):
        """Get remaining images for today.
        
        Returns:
            int: Remaining images (-1 for unlimited)
        """
        daily_limit = self.get_daily_limit()
        if daily_limit == -1:
            return -1  # Unlimited
        
        daily_count = self.get_daily_count()
        remaining = max(0, daily_limit - daily_count)
        return remaining
    
    def is_limit_exceeded(self):
        """Check if daily limit is exceeded.
        
        Returns:
            bool: True if limit is exceeded
        """
        daily_limit = self.get_daily_limit()
        if daily_limit == -1:
            return False  # Unlimited
        
        daily_count = self.get_daily_count()
        return daily_count >= daily_limit
    
    def can_generate_image(self):
        """Check if user can generate an image.
        
        Returns:
            tuple: (can_generate: bool, message: str)
        """
        if self.is_limit_exceeded():
            daily_limit = self.get_daily_limit()
            daily_count = self.get_daily_count()
            
            # Calculate time until reset
            reset_time = self._get_time_until_reset()
            
            message = (f"Daily limit reached: {daily_count}/{daily_limit} images generated today. "
                      f"Limit resets in {reset_time}.")
            
            return False, message
        
        return True, ""
    
    def _get_time_until_reset(self):
        """Get time remaining until daily reset.
        
        Returns:
            str: Human-readable time until reset
        """
        now = datetime.datetime.now()
        tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
        time_diff = tomorrow - now
        
        hours = time_diff.seconds // 3600
        minutes = (time_diff.seconds % 3600) // 60
        
        if hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
    
    def increment_usage(self):
        """Increment image generation count and emit signals.
        
        This should be called after each successful image generation.
        """
        self._check_daily_reset()
        
        # Increment counters
        self.usage_data["daily_count"] = self.usage_data.get("daily_count", 0) + 1
        self.usage_data["total_generated"] = self.usage_data.get("total_generated", 0) + 1
        self.usage_data["session_count"] = self.usage_data.get("session_count", 0) + 1
        
        # Set first generation time if not set
        if not self.usage_data.get("first_generation_time"):
            self.usage_data["first_generation_time"] = datetime.datetime.now().isoformat()
        
        # Save data
        self._save_usage_data()
        
        # Get current counts
        daily_count = self.usage_data["daily_count"]
        daily_limit = self.get_daily_limit()
        
        self.logger.info(f"Usage incremented: {daily_count}/{daily_limit if daily_limit != -1 else 'unlimited'}")
        
        # Emit usage update signal
        self.usage_updated.emit(daily_count, daily_limit)
        
        # Check for warnings and limits
        if daily_limit != -1:  # Only for limited plans
            remaining = daily_limit - daily_count
            
            # Emit warning when approaching limit
            if remaining <= 2 and remaining > 0:
                warning_msg = f"Approaching daily limit: {remaining} images remaining"
                self.limit_warning.emit(warning_msg, remaining)
            
            # Emit limit exceeded if we've reached it
            elif remaining <= 0:
                error_msg = f"Daily limit reached: {daily_count}/{daily_limit} images generated today"
                self.limit_exceeded.emit(error_msg)
    
    def get_usage_status_text(self):
        """Get formatted usage status text for display.
        
        Returns:
            str: Formatted status text
        """
        daily_count = self.get_daily_count()
        daily_limit = self.get_daily_limit()
        
        if daily_limit == -1:
            return f"Images generated today: {daily_count}"
        else:
            remaining = max(0, daily_limit - daily_count)
            return f"Images: {daily_count}/{daily_limit} (remaining: {remaining})"
    
    def get_detailed_status(self):
        """Get detailed status information.
        
        Returns:
            dict: Detailed status information
        """
        daily_count = self.get_daily_count()
        daily_limit = self.get_daily_limit()
        remaining = self.get_remaining_count()
        
        return {
            "daily_count": daily_count,
            "daily_limit": daily_limit,
            "remaining": remaining,
            "is_unlimited": daily_limit == -1,
            "is_limit_exceeded": self.is_limit_exceeded(),
            "time_until_reset": self._get_time_until_reset(),
            "session_count": self.usage_data.get("session_count", 0),
            "total_generated": self.usage_data.get("total_generated", 0)
        }
