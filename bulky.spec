# -*- mode: python ; coding: utf-8 -*-
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all submodules for comprehensive inclusion
ui_modules = collect_submodules('ui')
api_modules = collect_submodules('api')

# Add data files - include all necessary configuration and resource files
datas = [
    ('config.json', '.'),
    ('test_prompts.txt', '.'),
    ('ui/resources/app-logo.svg', 'ui/resources'),
    ('ui/resources/splash-screen.svg', 'ui/resources'),
    ('ui/resources/splash-screen-backup.svg', 'ui/resources'),
    ('ui/resources/README.md', 'ui/resources'),
    ('usage_data.json', '.') if os.path.exists('usage_data.json') else None,
    ('requirements.txt', '.'),
]

# Remove None entries
datas = [d for d in datas if d is not None]

# Create generated_images directory if it doesn't exist
if not os.path.exists('generated_images'):
    os.makedirs('generated_images')

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # PyQt6 modules
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtSvg',

        # Image processing
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageDraw',
        'PIL.ImageFont',

        # HTTP and API clients
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.exceptions',
        'requests.models',
        'requests.sessions',
        'requests.utils',
        'runware',

        # Security and storage
        'keyring',
        'keyring.backends',
        'keyring.backends.Windows',
        'keyring.backends.macOS',
        'keyring.backends.SecretService',
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.primitives.hashes',
        'cryptography.hazmat.primitives.kdf',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'wincred',

        # Standard library modules that might not be auto-detected
        'asyncio',
        'uuid',
        'base64',
        'json',
        'threading',
        'subprocess',
        'pathlib',
        'datetime',
        'platform',
        'hashlib',
        'typing',
        'signal',
        'traceback',
        'io',
        'sys',
        'os',
        'time',
        'shutil',

        # Application modules
        'config_manager',
        'app_controller',
        'license_manager',
        'usage_tracker',
        'secure_storage',
        'logger',

        # UI modules
        'ui',
        'ui.main_window',
        'ui.splash_screen',
        'ui.styles',
        'ui.api_key_manager',

        # API modules
        'api',
        'api.api_client',
        'api.runware_client',
        'api.replicate_client',
    ] + ui_modules + api_modules,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Pixeliano - Unlimited AI Images. One Click Away',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
