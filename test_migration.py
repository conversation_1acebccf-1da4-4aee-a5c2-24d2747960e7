#!/usr/bin/env python3
"""
Test script to check if the database migration can be accessed and run.
"""

import requests

def test_migration():
    """Test if the database migration is accessible."""
    
    print("Testing Database Migration Access")
    print("=" * 50)
    
    # Try to access the migration script
    migration_url = "https://bulkimages.azanx.com/licensing/database/migrate.php"
    
    try:
        print(f"Attempting to access: {migration_url}")
        response = requests.get(migration_url, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            print("✅ Migration script is accessible")
            print("\nMigration Output:")
            print("-" * 30)
            print(response.text)
            
            if "Migration completed successfully" in response.text:
                print("\n🎉 Database migration completed successfully!")
            elif "already exists" in response.text:
                print("\n✅ Database schema is already up to date")
            else:
                print("\n⚠️ Migration ran but check output for any issues")
                
        elif response.status_code == 404:
            print("❌ Migration script not found")
            print("The migration script may need to be uploaded to the server")
            
        elif response.status_code == 403:
            print("❌ Access denied to migration script")
            print("Server permissions may need to be adjusted")
            
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Migration request timed out")
        print("The migration may be taking longer than expected")
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to migration endpoint")
        print("Check if the server is accessible")
        
    except Exception as e:
        print(f"❌ Migration test error: {e}")
    
    print("\n" + "=" * 50)
    print("Migration Test Complete")
    print("\nIf the migration is not accessible, you may need to:")
    print("1. Upload the migrate.php file to the server")
    print("2. Ensure proper file permissions")
    print("3. Run the migration manually on the server")

if __name__ == "__main__":
    test_migration()
