# BulkAI Licensing System

A simple, lightweight licensing system for the BulkAI desktop application built with PHP, HTML, CSS, and SQLite.

## Features

### Admin Web Interface
- **Dashboard**: Overview of licenses, customers, and usage statistics
- **Customer Management**: Add, edit, and manage customers
- **License Management**: Create, activate, deactivate, and manage licenses
- **Usage Tracking**: Monitor image generation and API usage
- **Search & Filter**: Find customers and licenses quickly

### License Verification API
- **License Verification**: Validate license keys and check permissions
- **Device Activation**: Manage device activations per license
- **Usage Tracking**: Track daily image generation and API calls
- **Plan Enforcement**: Enforce daily limits and feature restrictions

### License Plans
1. **Free Plan**: 10 images/day, Flux Schnell model only, Together AI only
2. **Monthly Pro**: Unlimited images, all models and providers
3. **Yearly Pro**: Unlimited images, all models and providers
4. **Lifetime Pro**: One-time purchase, unlimited access

## Installation

### Requirements
- PHP 7.4 or higher
- SQLite support (usually included with PHP)
- Web server (Apache, Nginx, or PHP built-in server)

### Setup Steps

1. **Upload Files**: Copy the `licensing` folder to your web server

2. **Run Setup**: Visit `http://yourdomain.com/licensing/setup.php` to initialize the database

3. **Change Admin Password**: Edit `admin/index.php` and change the default credentials:
   ```php
   $admin_username = 'admin';
   $admin_password = 'your-secure-password'; // Change this!
   ```

4. **Access Admin Panel**: Visit `http://yourdomain.com/licensing/admin/`

5. **Configure Desktop App**: Update the license server URL in your desktop application

## API Endpoints

### Base URL
```
http://yourdomain.com/licensing/api/index.php
```

### 1. Verify License
**POST** `/verify-license`

Verify a license key and get permissions.

**Request:**
```json
{
    "license_key": "BULKAI-XXXXXXXX-XXXXXXXX-XXXXXXXX",
    "device_id": "unique-device-identifier"
}
```

**Response:**
```json
{
    "valid": true,
    "license": {
        "license_key": "BULKAI-XXXXXXXX-XXXXXXXX-XXXXXXXX",
        "customer_name": "John Doe",
        "plan_name": "Monthly Pro",
        "plan_type": "monthly",
        "daily_image_limit": -1,
        "features": {
            "models": ["all"],
            "providers": ["all"]
        },
        "expiry_date": "2025-02-01 12:00:00"
    },
    "usage": {
        "today_usage": 5,
        "daily_limit_exceeded": false
    },
    "device": {
        "activated": true,
        "device_id": "unique-device-identifier"
    }
}
```

### 2. Get License Info
**GET** `/license-info?license_key=KEY`

Get detailed license information.

### 3. Track Usage
**POST** `/usage-tracking`

Track image generation and API usage.

**Request:**
```json
{
    "license_key": "BULKAI-XXXXXXXX-XXXXXXXX-XXXXXXXX",
    "device_id": "unique-device-identifier",
    "images_generated": 1,
    "api_calls": 1,
    "provider_used": "runware_ai",
    "model_used": "flux_dev"
}
```

### 4. Activate License
**POST** `/activate-license`

Activate a license on a device.

**Request:**
```json
{
    "license_key": "BULKAI-XXXXXXXX-XXXXXXXX-XXXXXXXX",
    "device_id": "unique-device-identifier",
    "device_name": "John's Computer",
    "device_info": "{\"system\":\"Windows\",\"version\":\"10\"}"
}
```

## Desktop Application Integration

### 1. Install License Manager
Copy `license_manager.py` to your desktop application directory.

### 2. Initialize License Manager
```python
from license_manager import LicenseManager
from config_manager import ConfigManager

config_manager = ConfigManager()
license_manager = LicenseManager(
    config_manager, 
    license_server_url="http://yourdomain.com/licensing/api"
)
```

### 3. Check License Before Image Generation
```python
# Check if user can generate images
if license_manager.is_daily_limit_exceeded():
    show_upgrade_dialog()
    return

# Check if feature is allowed
if not license_manager.is_feature_allowed('all_models'):
    # Restrict to free models only
    available_models = ['flux_schnell']
else:
    # All models available
    available_models = get_all_models()

# Generate image
generate_image(prompt, model)

# Track usage
license_manager.track_usage(
    images_generated=1,
    provider_used="runware_ai",
    model_used="flux_dev"
)
```

### 4. License Activation Dialog
```python
def show_license_activation_dialog():
    license_key = get_license_key_from_user()
    result = license_manager.activate_license(license_key)
    
    if result.get('success'):
        show_success_message("License activated successfully!")
    else:
        show_error_message(result.get('error', 'Activation failed'))
```

## Database Schema

The system uses SQLite with the following tables:

- **customers**: Customer information
- **license_plans**: Available license plans
- **licenses**: License keys and their assignments
- **device_activations**: Device activations per license
- **usage_tracking**: Daily usage statistics

## Security Considerations

1. **Change Default Password**: Always change the default admin password
2. **HTTPS**: Use HTTPS in production for API calls
3. **API Keys**: Consider implementing API key authentication
4. **Rate Limiting**: Implement rate limiting for API endpoints
5. **Database Security**: Secure the SQLite database file

## Customization

### Adding New License Plans
1. Go to Admin Panel → Database
2. Add new plans to the `license_plans` table
3. Update desktop application logic to handle new plan types

### Modifying Features
Edit the `features` JSON field in license plans to add/remove features:
```json
{
    "models": ["all"],
    "providers": ["all"],
    "priority_support": true,
    "custom_feature": true
}
```

### Styling
Modify `admin/style.css` to customize the admin interface appearance.

## Troubleshooting

### Common Issues

1. **Database Permission Errors**
   - Ensure web server has write permissions to the `licensing/database/` directory

2. **API Connection Errors**
   - Check the license server URL in desktop application
   - Verify firewall settings

3. **License Verification Fails**
   - Check license key format
   - Verify license status in admin panel

### Debug Mode
Enable debug logging in the desktop application:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Support

For issues and questions:
1. Check the admin panel for license status
2. Review API logs for error messages
3. Test API endpoints using curl or Postman
4. Check desktop application logs

## License

This licensing system is part of the BulkAI project. Modify and use according to your needs.
