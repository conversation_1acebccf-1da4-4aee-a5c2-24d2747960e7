<?php
require_once 'config.php';
checkAuth();

$pdo = getDatabase();

// Get statistics
$stats = [];

// Total customers
$stmt = $pdo->query("SELECT COUNT(*) as count FROM customers WHERE status = 'active'");
$stats['customers'] = $stmt->fetch()['count'];

// Total licenses
$stmt = $pdo->query("SELECT COUNT(*) as count FROM licenses");
$stats['total_licenses'] = $stmt->fetch()['count'];

// Active licenses
$stmt = $pdo->query("SELECT COUNT(*) as count FROM licenses WHERE status = 'active'");
$stats['active_licenses'] = $stmt->fetch()['count'];

// Today's usage
$stmt = $pdo->query("SELECT SUM(images_generated) as count FROM usage_tracking WHERE usage_date = DATE('now')");
$result = $stmt->fetch();
$stats['today_images'] = $result['count'] ?? 0;

// Recent licenses
$stmt = $pdo->prepare("
    SELECT l.*, c.name as customer_name, c.email, p.plan_name 
    FROM licenses l 
    JOIN customers c ON l.customer_id = c.id 
    JOIN license_plans p ON l.plan_id = p.id 
    ORDER BY l.created_date DESC 
    LIMIT 10
");
$stmt->execute();
$recent_licenses = $stmt->fetchAll();

// Recent customers
$stmt = $pdo->prepare("SELECT * FROM customers ORDER BY created_date DESC LIMIT 5");
$stmt->execute();
$recent_customers = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - BulkAI License Admin</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="dashboard">
    <div class="header">
        <h1>BulkAI License Admin</h1>
        <nav class="nav">
            <a href="dashboard.php" class="active">Dashboard</a>
            <a href="customers.php">Customers</a>
            <a href="licenses.php">Licenses</a>
            <a href="usage.php">Usage</a>
            <a href="index.php?logout=1">Logout</a>
        </nav>
    </div>

    <div class="container">
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3><?php echo $stats['customers']; ?></h3>
                <p>Active Customers</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['total_licenses']; ?></h3>
                <p>Total Licenses</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['active_licenses']; ?></h3>
                <p>Active Licenses</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['today_images']; ?></h3>
                <p>Images Generated Today</p>
            </div>
        </div>

        <!-- Recent Licenses -->
        <div class="table-container">
            <div class="table-header">
                <h2>Recent Licenses</h2>
                <a href="licenses.php?action=create" class="btn btn-primary">Create New License</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>License Key</th>
                        <th>Customer</th>
                        <th>Plan</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Expires</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_licenses as $license): ?>
                    <tr>
                        <td><code><?php echo htmlspecialchars($license['license_key']); ?></code></td>
                        <td>
                            <?php echo htmlspecialchars($license['customer_name']); ?><br>
                            <small><?php echo htmlspecialchars($license['email']); ?></small>
                        </td>
                        <td><?php echo htmlspecialchars($license['plan_name']); ?></td>
                        <td><?php echo getStatusBadge($license['status']); ?></td>
                        <td><?php echo formatDate($license['created_date']); ?></td>
                        <td><?php echo $license['expiry_date'] ? formatDate($license['expiry_date']) : 'Never'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Recent Customers -->
        <div class="table-container">
            <div class="table-header">
                <h2>Recent Customers</h2>
                <a href="customers.php?action=create" class="btn btn-primary">Add New Customer</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Company</th>
                        <th>Status</th>
                        <th>Joined</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_customers as $customer): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($customer['name']); ?></td>
                        <td><?php echo htmlspecialchars($customer['email']); ?></td>
                        <td><?php echo htmlspecialchars($customer['company'] ?? 'N/A'); ?></td>
                        <td><?php echo getStatusBadge($customer['status']); ?></td>
                        <td><?php echo formatDate($customer['created_date']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
