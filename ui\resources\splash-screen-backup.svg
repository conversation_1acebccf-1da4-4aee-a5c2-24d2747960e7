<?xml version="1.0" encoding="utf-8" ?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="500">
    <!-- Background with gradient -->
    <defs>
        <linearGradient id="bg_gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#121212;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#2d2d2d;stop-opacity:1" />
        </linearGradient>
    </defs>
    <rect width="800" height="500" rx="15" ry="15" fill="url(#bg_gradient)"/>

    <!-- App Name -->
    <text x="400" y="200" font-family="'Segoe UI', 'SF Pro Display', 'Arial', sans-serif" font-size="48" font-weight="bold" fill="#FFFFFF" text-anchor="middle">Azanx Bulk AI Images</text>

    <!-- App Description -->
    <text x="400" y="260" font-family="'Segoe UI', 'SF Pro Display', 'Arial', sans-serif" font-size="20" fill="#CCCCCC" text-anchor="middle">Generate beautiful AI images in bulk</text>

    <!-- Loading animation - dots -->
    <g id="loading-dots">
        <circle cx="370" y="320" r="6" fill="#2d5af5">
            <animate attributeName="opacity" values="0.2;1;0.2" dur="1.5s" repeatCount="indefinite" begin="0s" />
        </circle>
        <circle cx="400" y="320" r="6" fill="#2d5af5">
            <animate attributeName="opacity" values="0.2;1;0.2" dur="1.5s" repeatCount="indefinite" begin="0.3s" />
        </circle>
        <circle cx="430" y="320" r="6" fill="#2d5af5">
            <animate attributeName="opacity" values="0.2;1;0.2" dur="1.5s" repeatCount="indefinite" begin="0.6s" />
        </circle>
    </g>

    <!-- Made with love in Pakistan -->
    <text x="400" y="460" font-family="'Segoe UI', 'SF Pro Display', 'Arial', sans-serif" font-size="14" font-style="italic" fill="#888888" text-anchor="middle">Made with love in Pakistan</text>
</svg>
