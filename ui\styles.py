"""
Styles for the application UI.
"""

class Styles:
    """Provides stylesheets for the application."""

    @staticmethod
    def get_dark_theme():
        """Get the dark theme stylesheet."""
        return """
            /* Main Window */
            QMainWindow, QDialog {
                background-color: #121212;
                color: #f0f0f0;
            }

            /* Widgets */
            QWidget {
                background-color: #121212;
                color: #f0f0f0;
                font-family: 'Segoe UI', 'SF Pro Display', 'Arial', sans-serif;
                font-size: 13px;
            }

            /* Labels */
            QLabel {
                color: #f0f0f0;
                font-size: 13px;
                padding: 2px;
            }

            #promptLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                margin-bottom: 5px;
            }

            /* Text inputs */
            QLineEdit, QTextEdit {
                background-color: #1e1e1e;
                border: 1px solid #333333;
                border-radius: 6px;
                padding: 8px;
                color: #f0f0f0;
                selection-background-color: #3d6aff;
            }

            QLineEdit:focus, QTextEdit:focus {
                border: 1px solid #3d6aff;
                background-color: #252525;
            }

            /* Combo box */
            QComboBox {
                background-color: #1e1e1e;
                border: 1px solid #333333;
                border-radius: 6px;
                padding: 8px;
                padding-right: 20px;
                color: #f0f0f0;
                min-height: 20px;
            }

            QComboBox:hover {
                background-color: #252525;
                border: 1px solid #444444;
            }

            QComboBox:focus {
                border: 1px solid #3d6aff;
            }

            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
            }

            QComboBox::down-arrow {
                image: url(ui/resources/down-arrow.png);
                width: 12px;
                height: 12px;
            }

            QComboBox QAbstractItemView {
                background-color: #1e1e1e;
                border: 1px solid #333333;
                border-radius: 6px;
                selection-background-color: #3d6aff;
                selection-color: #ffffff;
                padding: 4px;
            }

            /* Spin box */
            QSpinBox {
                background-color: #1e1e1e;
                border: 1px solid #333333;
                border-radius: 6px;
                padding: 8px;
                padding-right: 20px;
                color: #f0f0f0;
                min-height: 20px;
            }

            QSpinBox:hover {
                background-color: #252525;
                border: 1px solid #444444;
            }

            QSpinBox:focus {
                border: 1px solid #3d6aff;
            }

            QSpinBox::up-button, QSpinBox::down-button {
                subcontrol-origin: border;
                width: 20px;
                border-left: 1px solid #333333;
                background-color: #252525;
            }

            QSpinBox::up-button {
                subcontrol-position: top right;
                border-top-right-radius: 6px;
            }

            QSpinBox::down-button {
                subcontrol-position: bottom right;
                border-bottom-right-radius: 6px;
            }

            QSpinBox::up-arrow {
                image: url(ui/resources/up-arrow.png);
                width: 10px;
                height: 10px;
            }

            QSpinBox::down-arrow {
                image: url(ui/resources/down-arrow.png);
                width: 10px;
                height: 10px;
            }

            /* Buttons */
            QPushButton {
                background-color: #2d5af5;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
            }

            QPushButton:hover {
                background-color: #3d6aff;
            }

            QPushButton:pressed {
                background-color: #1d4ae5;
            }

            QPushButton:disabled {
                background-color: #333333;
                color: #666666;
            }

            /* Secondary button */
            QPushButton#secondaryButton {
                background-color: #333333;
                color: #f0f0f0;
                border: 1px solid #444444;
            }

            QPushButton#secondaryButton:hover {
                background-color: #444444;
            }

            QPushButton#secondaryButton:pressed {
                background-color: #222222;
            }

            /* Slider */
            QSlider::groove:horizontal {
                border: none;
                height: 6px;
                background: #333333;
                margin: 2px 0;
                border-radius: 3px;
            }

            QSlider::handle:horizontal {
                background: #2d5af5;
                border: none;
                width: 16px;
                height: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }

            QSlider::handle:horizontal:hover {
                background: #3d6aff;
            }

            QSlider::add-page:horizontal {
                background: #333333;
                border-radius: 3px;
            }

            QSlider::sub-page:horizontal {
                background: #2d5af5;
                border-radius: 3px;
            }

            /* Frames */
            #settingsFrame, #controlFrame {
                background-color: #1a1a1a;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }

            #imageFrame {
                background-color: #1a1a1a;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }

            /* Status label */
            #statusLabel {
                color: #888888;
                font-style: italic;
                padding: 5px;
                font-size: 12px;
            }

            /* Love label */
            #loveLabel {
                color: #888888;
                font-style: italic;
                padding: 5px;
                font-size: 12px;
            }

            /* Scroll area */
            QScrollArea {
                border: none;
                background-color: transparent;
            }

            /* Scroll bar */
            QScrollBar:vertical {
                border: none;
                background: #1a1a1a;
                width: 10px;
                margin: 0px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical {
                background: #444444;
                min-height: 20px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical:hover {
                background: #555555;
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
                height: 0px;
            }

            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }

            /* Tooltip */
            QToolTip {
                background-color: #1a1a1a;
                color: #f0f0f0;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 5px;
            }

            /* Menu */
            QMenuBar {
                background-color: #121212;
                color: #f0f0f0;
                border-bottom: 1px solid #333333;
            }

            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
            }

            QMenuBar::item:selected {
                background-color: #2d5af5;
                color: white;
            }

            QMenu {
                background-color: #1a1a1a;
                color: #f0f0f0;
                border: 1px solid #333333;
                border-radius: 6px;
                padding: 5px;
            }

            QMenu::item {
                padding: 8px 30px 8px 20px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #2d5af5;
                color: white;
            }

            QMenu::separator {
                height: 1px;
                background-color: #333333;
                margin: 5px 10px;
            }

            /* Tab widget */
            QTabWidget::pane {
                border: 1px solid #333333;
                border-radius: 6px;
                background-color: #1a1a1a;
            }

            QTabBar::tab {
                background-color: #1a1a1a;
                color: #888888;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                padding: 10px 15px;
                margin-right: 2px;
            }

            QTabBar::tab:selected {
                background-color: #2d5af5;
                color: white;
            }

            QTabBar::tab:hover:!selected {
                background-color: #252525;
                color: #f0f0f0;
            }
        """

    @staticmethod
    def get_light_theme():
        """Get the light theme stylesheet."""
        return """
            /* Main Window */
            QMainWindow, QDialog {
                background-color: #f5f5f5;
                color: #333333;
            }

            /* Widgets */
            QWidget {
                background-color: #f5f5f5;
                color: #333333;
                font-family: 'Segoe UI', 'SF Pro Display', 'Arial', sans-serif;
                font-size: 13px;
            }

            /* Labels */
            QLabel {
                color: #333333;
                font-size: 13px;
                padding: 2px;
            }

            #promptLabel {
                font-size: 16px;
                font-weight: bold;
                color: #000000;
                margin-bottom: 5px;
            }

            /* Text inputs */
            QLineEdit, QTextEdit {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 8px;
                color: #333333;
                selection-background-color: #3d6aff;
            }

            QLineEdit:focus, QTextEdit:focus {
                border: 1px solid #3d6aff;
                background-color: #ffffff;
            }

            /* Combo box */
            QComboBox {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 8px;
                padding-right: 20px;
                color: #333333;
                min-height: 20px;
            }

            QComboBox:hover {
                background-color: #f0f0f0;
                border: 1px solid #bbbbbb;
            }

            QComboBox:focus {
                border: 1px solid #3d6aff;
            }

            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
            }

            QComboBox::down-arrow {
                image: url(ui/resources/down-arrow-dark.png);
                width: 12px;
                height: 12px;
            }

            QComboBox QAbstractItemView {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 6px;
                selection-background-color: #3d6aff;
                selection-color: #ffffff;
                padding: 4px;
            }

            /* Spin box */
            QSpinBox {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 8px;
                padding-right: 20px;
                color: #333333;
                min-height: 20px;
            }

            QSpinBox:hover {
                background-color: #f0f0f0;
                border: 1px solid #bbbbbb;
            }

            QSpinBox:focus {
                border: 1px solid #3d6aff;
            }

            QSpinBox::up-button, QSpinBox::down-button {
                subcontrol-origin: border;
                width: 20px;
                border-left: 1px solid #cccccc;
                background-color: #f0f0f0;
            }

            QSpinBox::up-button {
                subcontrol-position: top right;
                border-top-right-radius: 6px;
            }

            QSpinBox::down-button {
                subcontrol-position: bottom right;
                border-bottom-right-radius: 6px;
            }

            QSpinBox::up-arrow {
                image: url(ui/resources/up-arrow-dark.png);
                width: 10px;
                height: 10px;
            }

            QSpinBox::down-arrow {
                image: url(ui/resources/down-arrow-dark.png);
                width: 10px;
                height: 10px;
            }

            /* Buttons */
            QPushButton {
                background-color: #2d5af5;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
            }

            QPushButton:hover {
                background-color: #3d6aff;
            }

            QPushButton:pressed {
                background-color: #1d4ae5;
            }

            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }

            /* Secondary button */
            QPushButton#secondaryButton {
                background-color: #e0e0e0;
                color: #333333;
                border: 1px solid #cccccc;
            }

            QPushButton#secondaryButton:hover {
                background-color: #d0d0d0;
            }

            QPushButton#secondaryButton:pressed {
                background-color: #c0c0c0;
            }

            /* Slider */
            QSlider::groove:horizontal {
                border: none;
                height: 6px;
                background: #cccccc;
                margin: 2px 0;
                border-radius: 3px;
            }

            QSlider::handle:horizontal {
                background: #2d5af5;
                border: none;
                width: 16px;
                height: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }

            QSlider::handle:horizontal:hover {
                background: #3d6aff;
            }

            QSlider::add-page:horizontal {
                background: #cccccc;
                border-radius: 3px;
            }

            QSlider::sub-page:horizontal {
                background: #2d5af5;
                border-radius: 3px;
            }

            /* Frames */
            #settingsFrame, #controlFrame {
                background-color: #ffffff;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                border: 1px solid #e0e0e0;
            }

            #imageFrame {
                background-color: #ffffff;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                border: 1px solid #e0e0e0;
            }

            /* Status label */
            #statusLabel {
                color: #666666;
                font-style: italic;
                padding: 5px;
                font-size: 12px;
            }

            /* Love label */
            #loveLabel {
                color: #666666;
                font-style: italic;
                padding: 5px;
                font-size: 12px;
            }

            /* Scroll area */
            QScrollArea {
                border: none;
                background-color: transparent;
            }

            /* Scroll bar */
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 10px;
                margin: 0px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical {
                background: #cccccc;
                min-height: 20px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical:hover {
                background: #bbbbbb;
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
                height: 0px;
            }

            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }

            /* Tooltip */
            QToolTip {
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 5px;
            }

            /* Menu */
            QMenuBar {
                background-color: #f5f5f5;
                color: #333333;
                border-bottom: 1px solid #e0e0e0;
            }

            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
            }

            QMenuBar::item:selected {
                background-color: #2d5af5;
                color: white;
            }

            QMenu {
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 5px;
            }

            QMenu::item {
                padding: 8px 30px 8px 20px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #2d5af5;
                color: white;
            }

            QMenu::separator {
                height: 1px;
                background-color: #e0e0e0;
                margin: 5px 10px;
            }

            /* Tab widget */
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 6px;
                background-color: #ffffff;
            }

            QTabBar::tab {
                background-color: #f0f0f0;
                color: #666666;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                padding: 10px 15px;
                margin-right: 2px;
            }

            QTabBar::tab:selected {
                background-color: #2d5af5;
                color: white;
            }

            QTabBar::tab:hover:!selected {
                background-color: #e0e0e0;
                color: #333333;
            }
        """
