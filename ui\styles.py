"""
Modern styles for the BulkAI application UI with card-based design.
"""

class Styles:
    """Provides modern stylesheets for the application."""

    @staticmethod
    def get_dark_theme():
        """Get the modern dark theme stylesheet with card-based design."""
        return """
            /* Main Window */
            QMainWindow, QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0a0a0a, stop:0.5 #121212, stop:1 #1a1a1a);
                color: #f0f0f0;
            }

            /* Widgets */
            QWidget {
                background-color: transparent;
                color: #f0f0f0;
                font-family: 'Segoe UI', 'SF Pro Display', 'Inter', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 400;
            }

            /* Labels */
            QLabel {
                color: #f0f0f0;
                font-size: 14px;
                padding: 4px;
                font-weight: 400;
            }

            #promptLabel {
                font-size: 18px;
                font-weight: 600;
                color: #ffffff;
                margin-bottom: 8px;
                letter-spacing: 0.5px;
            }

            /* Modern Text inputs with glassmorphism */
            QLineEdit, QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 30, 30, 0.9), stop:1 rgba(25, 25, 25, 0.9));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 12px 16px;
                color: #f0f0f0;
                selection-background-color: #4f46e5;
                font-size: 14px;
                font-weight: 400;
            }

            QLineEdit:focus, QTextEdit:focus {
                border: 2px solid #4f46e5;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(35, 35, 35, 0.95), stop:1 rgba(30, 30, 30, 0.95));
            }

            QLineEdit:hover, QTextEdit:hover {
                border: 1px solid rgba(255, 255, 255, 0.2);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(35, 35, 35, 0.9), stop:1 rgba(30, 30, 30, 0.9));
            }

            /* Modern Combo box */
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 30, 30, 0.9), stop:1 rgba(25, 25, 25, 0.9));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 12px 16px;
                padding-right: 40px;
                color: #f0f0f0;
                min-height: 20px;
                font-size: 14px;
                font-weight: 400;
            }

            QComboBox:hover {
                border: 1px solid rgba(255, 255, 255, 0.2);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(35, 35, 35, 0.9), stop:1 rgba(30, 30, 30, 0.9));
            }

            QComboBox:focus {
                border: 2px solid #4f46e5;
            }

            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 30px;
                border-left: none;
                border-top-right-radius: 12px;
                border-bottom-right-radius: 12px;
                background: transparent;
            }

            QComboBox::down-arrow {
                image: url(ui/resources/down-arrow.png);
                width: 14px;
                height: 14px;
            }

            QComboBox QAbstractItemView {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 30, 30, 0.95), stop:1 rgba(25, 25, 25, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                selection-background-color: #4f46e5;
                selection-color: #ffffff;
                padding: 8px;
            }

            /* Modern Spin box */
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 30, 30, 0.9), stop:1 rgba(25, 25, 25, 0.9));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 12px 16px;
                padding-right: 40px;
                color: #f0f0f0;
                min-height: 20px;
                font-size: 14px;
                font-weight: 400;
            }

            QSpinBox:hover {
                border: 1px solid rgba(255, 255, 255, 0.2);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(35, 35, 35, 0.9), stop:1 rgba(30, 30, 30, 0.9));
            }

            QSpinBox:focus {
                border: 2px solid #4f46e5;
            }

            QSpinBox::up-button, QSpinBox::down-button {
                subcontrol-origin: border;
                width: 30px;
                border-left: 1px solid rgba(255, 255, 255, 0.1);
                background: transparent;
            }

            QSpinBox::up-button {
                subcontrol-position: top right;
                border-top-right-radius: 12px;
            }

            QSpinBox::down-button {
                subcontrol-position: bottom right;
                border-bottom-right-radius: 12px;
            }

            QSpinBox::up-arrow {
                image: url(ui/resources/up-arrow.png);
                width: 12px;
                height: 12px;
            }

            QSpinBox::down-arrow {
                image: url(ui/resources/down-arrow.png);
                width: 12px;
                height: 12px;
            }

            /* Modern Buttons with gradient and shadow */
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4f46e5, stop:0.5 #6366f1, stop:1 #7c3aed);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 14px 24px;
                font-weight: 600;
                font-size: 14px;
                min-height: 24px;
                letter-spacing: 0.5px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5b52e8, stop:0.5 #7075f4, stop:1 #8b5cf6);
            }

            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3730a3, stop:0.5 #4338ca, stop:1 #5b21b6);
            }

            QPushButton:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(60, 60, 60, 0.5), stop:1 rgba(80, 80, 80, 0.5));
                color: rgba(255, 255, 255, 0.4);
            }

            /* Modern Secondary button */
            QPushButton#secondaryButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(60, 60, 60, 0.8), stop:1 rgba(40, 40, 40, 0.8));
                color: #f0f0f0;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 14px 24px;
                font-weight: 500;
            }

            QPushButton#secondaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(70, 70, 70, 0.9), stop:1 rgba(50, 50, 50, 0.9));
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            QPushButton#secondaryButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 30, 30, 0.9), stop:1 rgba(20, 20, 20, 0.9));
            }

            /* Modern Slider */
            QSlider::groove:horizontal {
                border: none;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(60, 60, 60, 0.8), stop:1 rgba(40, 40, 40, 0.8));
                margin: 4px 0;
                border-radius: 4px;
            }

            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4f46e5, stop:1 #7c3aed);
                border: 2px solid rgba(255, 255, 255, 0.2);
                width: 20px;
                height: 20px;
                margin: -8px 0;
                border-radius: 12px;
            }

            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5b52e8, stop:1 #8b5cf6);
            }

            QSlider::add-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(60, 60, 60, 0.8), stop:1 rgba(40, 40, 40, 0.8));
                border-radius: 4px;
            }

            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4f46e5, stop:1 #7c3aed);
                border-radius: 4px;
            }

            /* Modern Card-based Frames */
            #settingsFrame, #controlFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(26, 26, 26, 0.95), stop:1 rgba(20, 20, 20, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 16px;
                padding: 24px;
                margin: 8px;
            }

            #imageFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(26, 26, 26, 0.95), stop:1 rgba(20, 20, 20, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 16px;
                padding: 24px;
                margin: 8px;
            }

            /* Modern Status label */
            #statusLabel {
                color: rgba(255, 255, 255, 0.6);
                font-style: italic;
                padding: 8px;
                font-size: 13px;
                font-weight: 400;
            }

            /* Modern Love label */
            #loveLabel {
                color: rgba(255, 255, 255, 0.6);
                font-style: italic;
                padding: 8px;
                font-size: 13px;
                font-weight: 400;
            }

            /* Modern Scroll area */
            QScrollArea {
                border: none;
                background-color: transparent;
                border-radius: 12px;
            }

            /* Modern Scroll bar */
            QScrollBar:vertical {
                border: none;
                background: rgba(26, 26, 26, 0.5);
                width: 12px;
                margin: 0px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(79, 70, 229, 0.8), stop:1 rgba(124, 58, 237, 0.8));
                min-height: 30px;
                border-radius: 6px;
                margin: 2px;
            }

            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(91, 82, 232, 0.9), stop:1 rgba(139, 92, 246, 0.9));
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
                height: 0px;
            }

            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }

            /* Modern Tooltip */
            QToolTip {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(26, 26, 26, 0.95), stop:1 rgba(20, 20, 20, 0.95));
                color: #f0f0f0;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                padding: 8px 12px;
            }

            /* Modern Menu */
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(18, 18, 18, 0.95), stop:1 rgba(12, 12, 12, 0.95));
                color: #f0f0f0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 4px;
            }

            QMenuBar::item {
                background-color: transparent;
                padding: 8px 16px;
                border-radius: 8px;
                margin: 2px;
            }

            QMenuBar::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4f46e5, stop:1 #7c3aed);
                color: white;
            }

            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(26, 26, 26, 0.95), stop:1 rgba(20, 20, 20, 0.95));
                color: #f0f0f0;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 8px;
            }

            QMenu::item {
                padding: 10px 30px 10px 20px;
                border-radius: 8px;
                margin: 2px;
            }

            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4f46e5, stop:1 #7c3aed);
                color: white;
            }

            QMenu::separator {
                height: 1px;
                background: rgba(255, 255, 255, 0.1);
                margin: 8px 16px;
            }

            /* Modern Tab widget */
            QTabWidget::pane {
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 16px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(26, 26, 26, 0.95), stop:1 rgba(20, 20, 20, 0.95));
                margin-top: 8px;
            }

            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(26, 26, 26, 0.8), stop:1 rgba(20, 20, 20, 0.8));
                color: rgba(255, 255, 255, 0.6);
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                padding: 12px 20px;
                margin-right: 4px;
                font-weight: 500;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-bottom: none;
            }

            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4f46e5, stop:1 #7c3aed);
                color: white;
                font-weight: 600;
            }

            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(35, 35, 35, 0.9), stop:1 rgba(30, 30, 30, 0.9));
                color: #f0f0f0;
            }

            /* Modern Progress Bar */
            QProgressBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(60, 60, 60, 0.8), stop:1 rgba(40, 40, 40, 0.8));
                border: none;
                border-radius: 8px;
                text-align: center;
                font-weight: 600;
                font-size: 12px;
                color: #f0f0f0;
                min-height: 16px;
            }

            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4f46e5, stop:0.5 #6366f1, stop:1 #7c3aed);
                border-radius: 8px;
                margin: 1px;
            }

            /* Modern Status Bar */
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(18, 18, 18, 0.95), stop:1 rgba(12, 12, 12, 0.95));
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                padding: 4px;
            }
        """

    @staticmethod
    def get_light_theme():
        """Get the modern light theme stylesheet with card-based design."""
        return """
            /* Main Window */
            QMainWindow, QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.5 #f1f5f9, stop:1 #e2e8f0);
                color: #1e293b;
            }

            /* Widgets */
            QWidget {
                background-color: transparent;
                color: #1e293b;
                font-family: 'Segoe UI', 'SF Pro Display', 'Inter', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 400;
            }

            /* Labels */
            QLabel {
                color: #1e293b;
                font-size: 14px;
                padding: 4px;
                font-weight: 400;
            }

            #promptLabel {
                font-size: 18px;
                font-weight: 600;
                color: #0f172a;
                margin-bottom: 8px;
                letter-spacing: 0.5px;
            }

            /* Modern Text inputs with glassmorphism */
            QLineEdit, QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9), stop:1 rgba(248, 250, 252, 0.9));
                border: 1px solid rgba(148, 163, 184, 0.3);
                border-radius: 12px;
                padding: 12px 16px;
                color: #1e293b;
                selection-background-color: #4f46e5;
                font-size: 14px;
                font-weight: 400;
            }

            QLineEdit:focus, QTextEdit:focus {
                border: 2px solid #4f46e5;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.95));
            }

            QLineEdit:hover, QTextEdit:hover {
                border: 1px solid rgba(148, 163, 184, 0.5);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.95));
            }

            /* Combo box */
            QComboBox {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 8px;
                padding-right: 20px;
                color: #333333;
                min-height: 20px;
            }

            QComboBox:hover {
                background-color: #f0f0f0;
                border: 1px solid #bbbbbb;
            }

            QComboBox:focus {
                border: 1px solid #3d6aff;
            }

            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
            }

            QComboBox::down-arrow {
                image: url(ui/resources/down-arrow-dark.png);
                width: 12px;
                height: 12px;
            }

            QComboBox QAbstractItemView {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 6px;
                selection-background-color: #3d6aff;
                selection-color: #ffffff;
                padding: 4px;
            }

            /* Spin box */
            QSpinBox {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 8px;
                padding-right: 20px;
                color: #333333;
                min-height: 20px;
            }

            QSpinBox:hover {
                background-color: #f0f0f0;
                border: 1px solid #bbbbbb;
            }

            QSpinBox:focus {
                border: 1px solid #3d6aff;
            }

            QSpinBox::up-button, QSpinBox::down-button {
                subcontrol-origin: border;
                width: 20px;
                border-left: 1px solid #cccccc;
                background-color: #f0f0f0;
            }

            QSpinBox::up-button {
                subcontrol-position: top right;
                border-top-right-radius: 6px;
            }

            QSpinBox::down-button {
                subcontrol-position: bottom right;
                border-bottom-right-radius: 6px;
            }

            QSpinBox::up-arrow {
                image: url(ui/resources/up-arrow-dark.png);
                width: 10px;
                height: 10px;
            }

            QSpinBox::down-arrow {
                image: url(ui/resources/down-arrow-dark.png);
                width: 10px;
                height: 10px;
            }

            /* Buttons */
            QPushButton {
                background-color: #2d5af5;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
            }

            QPushButton:hover {
                background-color: #3d6aff;
            }

            QPushButton:pressed {
                background-color: #1d4ae5;
            }

            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }

            /* Secondary button */
            QPushButton#secondaryButton {
                background-color: #e0e0e0;
                color: #333333;
                border: 1px solid #cccccc;
            }

            QPushButton#secondaryButton:hover {
                background-color: #d0d0d0;
            }

            QPushButton#secondaryButton:pressed {
                background-color: #c0c0c0;
            }

            /* Slider */
            QSlider::groove:horizontal {
                border: none;
                height: 6px;
                background: #cccccc;
                margin: 2px 0;
                border-radius: 3px;
            }

            QSlider::handle:horizontal {
                background: #2d5af5;
                border: none;
                width: 16px;
                height: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }

            QSlider::handle:horizontal:hover {
                background: #3d6aff;
            }

            QSlider::add-page:horizontal {
                background: #cccccc;
                border-radius: 3px;
            }

            QSlider::sub-page:horizontal {
                background: #2d5af5;
                border-radius: 3px;
            }

            /* Modern Card-based Frames */
            #settingsFrame, #controlFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.95));
                border: 1px solid rgba(148, 163, 184, 0.2);
                border-radius: 16px;
                padding: 24px;
                margin: 8px;
            }

            #imageFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.95));
                border: 1px solid rgba(148, 163, 184, 0.2);
                border-radius: 16px;
                padding: 24px;
                margin: 8px;
            }

            /* Modern Status label */
            #statusLabel {
                color: rgba(30, 41, 59, 0.6);
                font-style: italic;
                padding: 8px;
                font-size: 13px;
                font-weight: 400;
            }

            /* Modern Love label */
            #loveLabel {
                color: rgba(30, 41, 59, 0.6);
                font-style: italic;
                padding: 8px;
                font-size: 13px;
                font-weight: 400;
            }

            /* Scroll area */
            QScrollArea {
                border: none;
                background-color: transparent;
            }

            /* Scroll bar */
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 10px;
                margin: 0px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical {
                background: #cccccc;
                min-height: 20px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical:hover {
                background: #bbbbbb;
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
                height: 0px;
            }

            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }

            /* Tooltip */
            QToolTip {
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 5px;
            }

            /* Menu */
            QMenuBar {
                background-color: #f5f5f5;
                color: #333333;
                border-bottom: 1px solid #e0e0e0;
            }

            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
            }

            QMenuBar::item:selected {
                background-color: #2d5af5;
                color: white;
            }

            QMenu {
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 5px;
            }

            QMenu::item {
                padding: 8px 30px 8px 20px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #2d5af5;
                color: white;
            }

            QMenu::separator {
                height: 1px;
                background-color: #e0e0e0;
                margin: 5px 10px;
            }

            /* Tab widget */
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 6px;
                background-color: #ffffff;
            }

            QTabBar::tab {
                background-color: #f0f0f0;
                color: #666666;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                padding: 10px 15px;
                margin-right: 2px;
            }

            QTabBar::tab:selected {
                background-color: #2d5af5;
                color: white;
            }

            QTabBar::tab:hover:!selected {
                background-color: #e0e0e0;
                color: #333333;
            }

            /* Modern Progress Bar */
            QProgressBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(226, 232, 240, 0.8), stop:1 rgba(203, 213, 225, 0.8));
                border: none;
                border-radius: 8px;
                text-align: center;
                font-weight: 600;
                font-size: 12px;
                color: #1e293b;
                min-height: 16px;
            }

            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4f46e5, stop:0.5 #6366f1, stop:1 #7c3aed);
                border-radius: 8px;
                margin: 1px;
            }

            /* Modern Status Bar */
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 0.95), stop:1 rgba(241, 245, 249, 0.95));
                border-top: 1px solid rgba(148, 163, 184, 0.2);
                color: rgba(30, 41, 59, 0.8);
                font-size: 12px;
                padding: 4px;
            }
        """
