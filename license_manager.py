"""
License Manager for BulkAI Desktop Application
Handles license verification, activation, and usage tracking.
"""

import json
import requests
import hashlib
import platform
import uuid
from datetime import datetime
from logger import get_logger


class LicenseManager:
    """Manages licensing for the BulkAI desktop application."""

    def __init__(self, config_manager, license_server_url="https://bulkimages.azanx.com/licensing/api"):
        """Initialize the license manager.

        Args:
            config_manager: ConfigManager instance
            license_server_url: URL of the license verification server
        """
        self.config_manager = config_manager
        self.license_server_url = license_server_url.rstrip('/')
        self.logger = get_logger()
        self.device_id = self._get_device_id()

        # Cache for license info to avoid frequent API calls
        self._license_cache = None
        self._cache_timestamp = None
        self._cache_duration = 300  # 5 minutes

    def _get_device_id(self):
        """Generate a unique device ID based on hardware characteristics."""
        try:
            # Try to get existing device ID from config
            device_id = self.config_manager.get_setting("device_id")
            if device_id:
                return device_id

            # Generate new device ID based on hardware
            machine_id = platform.machine()
            processor = platform.processor()
            system = platform.system()
            node = platform.node()

            # Create a hash of hardware characteristics
            hardware_string = f"{machine_id}-{processor}-{system}-{node}"
            device_id = hashlib.sha256(hardware_string.encode()).hexdigest()[:16]

            # Store device ID in config
            self.config_manager.set_setting("device_id", device_id)
            self.logger.info(f"Generated new device ID: {device_id}")

            return device_id

        except Exception as e:
            self.logger.error(f"Error generating device ID: {e}")
            # Fallback to random UUID
            device_id = str(uuid.uuid4())[:16]
            self.config_manager.set_setting("device_id", device_id)
            return device_id

    def get_license_key(self):
        """Get the current license key from config."""
        return self.config_manager.get_setting("license_key", "")

    def set_license_key(self, license_key):
        """Set the license key in config."""
        self.config_manager.set_setting("license_key", license_key)
        # Clear cache when license key changes
        self._license_cache = None
        self._cache_timestamp = None

    def verify_license(self, license_key=None):
        """Verify the license with the server.

        Args:
            license_key: License key to verify (uses stored key if None)

        Returns:
            dict: License verification result
        """
        if not license_key:
            license_key = self.get_license_key()

        if not license_key:
            return {
                'valid': False,
                'error': 'No license key provided',
                'plan_type': 'free'
            }

        # Check cache first
        if self._is_cache_valid():
            self.logger.debug("Using cached license verification result")
            return self._license_cache

        try:
            response = requests.post(
                f"{self.license_server_url}/?action=verify-license",
                json={
                    'license_key': license_key,
                    'device_id': self.device_id
                },
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()

                # Cache the result
                self._license_cache = result
                self._cache_timestamp = datetime.now()

                self.logger.info(f"License verification successful: {result.get('valid', False)}")
                return result
            else:
                error_msg = f"License verification failed: HTTP {response.status_code}"
                self.logger.error(error_msg)
                return {
                    'valid': False,
                    'error': error_msg,
                    'plan_type': 'free'
                }

        except requests.exceptions.RequestException as e:
            error_msg = f"License verification network error: {e}"
            self.logger.error(error_msg)
            return {
                'valid': False,
                'error': error_msg,
                'plan_type': 'free'
            }

    def activate_license(self, license_key):
        """Activate a license on this device.

        Args:
            license_key: License key to activate

        Returns:
            dict: Activation result
        """
        try:
            device_info = {
                'system': platform.system(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'python_version': platform.python_version()
            }

            activation_url = f"{self.license_server_url}/?action=activate-license"
            self.logger.info(f"Attempting license activation at: {activation_url}")

            response = requests.post(
                activation_url,
                json={
                    'license_key': license_key,
                    'device_id': self.device_id,
                    'device_name': platform.node(),
                    'device_info': json.dumps(device_info)
                },
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )

            self.logger.info(f"License activation response: HTTP {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    # Store the license key
                    self.set_license_key(license_key)
                    self.logger.info("License activated successfully")
                return result
            else:
                # Log response content for debugging
                try:
                    error_response = response.json()
                    error_msg = f"License activation failed: HTTP {response.status_code} - {error_response.get('error', 'Unknown error')}"
                except:
                    error_msg = f"License activation failed: HTTP {response.status_code} - {response.text}"

                self.logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }

        except requests.exceptions.RequestException as e:
            error_msg = f"License activation network error: {e}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def track_usage(self, images_generated=1, api_calls=1, provider_used="", model_used=""):
        """Track usage statistics.

        Args:
            images_generated: Number of images generated
            api_calls: Number of API calls made
            provider_used: AI provider used
            model_used: Model used
        """
        license_key = self.get_license_key()
        if not license_key:
            return  # No license to track usage for

        try:
            response = requests.post(
                f"{self.license_server_url}/?action=usage-tracking",
                json={
                    'license_key': license_key,
                    'device_id': self.device_id,
                    'images_generated': images_generated,
                    'api_calls': api_calls,
                    'provider_used': provider_used,
                    'model_used': model_used
                },
                timeout=5
            )

            if response.status_code == 200:
                self.logger.debug("Usage tracked successfully")
            else:
                self.logger.warning(f"Usage tracking failed: HTTP {response.status_code}")

        except requests.exceptions.RequestException as e:
            self.logger.warning(f"Usage tracking network error: {e}")

    def get_plan_type(self):
        """Get the current plan type.

        Returns:
            str: Plan type ('free', 'monthly', 'yearly', 'lifetime')
        """
        verification = self.verify_license()
        if verification.get('valid'):
            return verification.get('license', {}).get('plan_type', 'free')
        return 'free'

    def is_feature_allowed(self, feature):
        """Check if a feature is allowed under the current license.

        Args:
            feature: Feature to check ('all_models', 'all_providers', etc.)

        Returns:
            bool: True if feature is allowed
        """
        verification = self.verify_license()
        if not verification.get('valid'):
            # Free plan restrictions
            if feature == 'all_models':
                return False
            if feature == 'all_providers':
                return False
            return True

        features = verification.get('license', {}).get('features', {})

        if feature == 'all_models':
            return features.get('models') == ['all']
        elif feature == 'all_providers':
            return features.get('providers') == ['all']

        return True

    def get_daily_usage_limit(self):
        """Get the daily image generation limit.

        Returns:
            int: Daily limit (-1 for unlimited, positive number for limit)
        """
        verification = self.verify_license()
        if not verification.get('valid'):
            return 10  # Free plan limit

        return verification.get('license', {}).get('daily_image_limit', -1)

    def get_today_usage(self):
        """Get today's usage count.

        Returns:
            int: Number of images generated today
        """
        verification = self.verify_license()
        return verification.get('usage', {}).get('today_usage', 0)

    def is_daily_limit_exceeded(self):
        """Check if daily limit is exceeded.

        Returns:
            bool: True if daily limit is exceeded
        """
        verification = self.verify_license()
        return verification.get('usage', {}).get('daily_limit_exceeded', False)

    def _is_cache_valid(self):
        """Check if the license cache is still valid."""
        if not self._license_cache or not self._cache_timestamp:
            return False

        elapsed = (datetime.now() - self._cache_timestamp).total_seconds()
        return elapsed < self._cache_duration

    def clear_cache(self):
        """Clear the license verification cache."""
        self._license_cache = None
        self._cache_timestamp = None

    def is_trial_plan(self):
        """Check if user is on trial/free plan.

        Returns:
            bool: True if user is on trial/free plan
        """
        plan_type = self.get_plan_type()
        return plan_type == 'free'

    def is_pro_plan(self):
        """Check if user has a Pro plan.

        Returns:
            bool: True if user has any Pro plan
        """
        plan_type = self.get_plan_type()
        return plan_type in ['monthly', 'yearly', 'lifetime']

    def get_usage_status_text(self):
        """Get user-friendly usage status text.

        Returns:
            str: Status text like "Trial Plan: 5/10 images used today"
        """
        if self.is_trial_plan():
            today_usage = self.get_today_usage()
            daily_limit = self.get_daily_usage_limit()
            return f"Trial Plan: {today_usage}/{daily_limit} images used today"
        else:
            verification = self.verify_license()
            if verification.get('valid'):
                license_info = verification.get('license', {})
                plan_name = license_info.get('plan_name', 'Pro Plan')
                today_usage = self.get_today_usage()
                return f"{plan_name}: {today_usage} images generated today"
            else:
                return "Trial Plan: License verification failed"

    def get_plan_status_text(self):
        """Get user-friendly plan status text.

        Returns:
            str: Status text like "Trial Plan - Limited Features"
        """
        if self.is_trial_plan():
            return "Trial Plan - Limited Features"
        else:
            verification = self.verify_license()
            if verification.get('valid'):
                license_info = verification.get('license', {})
                plan_name = license_info.get('plan_name', 'Pro Plan')
                return f"{plan_name} - All Features Unlocked"
            else:
                return "Trial Plan - Limited Features"

    def can_access_bulk_generation(self):
        """Check if user can access bulk generation feature.

        Returns:
            bool: True if bulk generation is allowed
        """
        return self.is_pro_plan()

    def can_access_runware_ai(self):
        """Check if user can access Runware AI.

        Returns:
            bool: True if Runware AI is allowed
        """
        return self.is_feature_allowed('all_providers')

    def get_allowed_models(self):
        """Get list of models allowed for current plan.

        Returns:
            list: List of allowed model IDs
        """
        if self.is_trial_plan():
            return ["black-forest-labs/FLUX.1-schnell-Free"]
        else:
            # Pro users get all models - return None to indicate no restrictions
            return None

    def get_upgrade_message(self, feature_name="this feature"):
        """Get upgrade message for blocked features.

        Args:
            feature_name: Name of the feature being blocked

        Returns:
            str: User-friendly upgrade message
        """
        return f"{feature_name} requires a Pro license. Please upgrade to access this feature."

    def deactivate_license(self):
        """Deactivate the current license on this device.

        Returns:
            dict: Deactivation result
        """
        license_key = self.get_license_key()
        if not license_key:
            return {
                'success': False,
                'error': 'No license key to deactivate'
            }

        try:
            response = requests.post(
                f"{self.license_server_url}/?action=deactivate-license",
                json={
                    'license_key': license_key,
                    'device_id': self.device_id
                },
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )

            self.logger.info(f"License deactivation response: HTTP {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    # Remove the license key from local storage
                    self.set_license_key("")
                    self.clear_cache()
                    self.logger.info("License deactivated successfully")
                return result
            else:
                # Log response content for debugging
                try:
                    error_response = response.json()
                    error_msg = f"License deactivation failed: HTTP {response.status_code} - {error_response.get('error', 'Unknown error')}"

                    # Include debug info if available
                    if 'debug_info' in error_response:
                        debug_info = error_response['debug_info']
                        self.logger.error(f"Debug info: {debug_info}")
                        error_msg += f"\nDebug info: {debug_info}"

                except:
                    error_msg = f"License deactivation failed: HTTP {response.status_code} - {response.text}"

                self.logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg
                }

        except requests.exceptions.RequestException as e:
            error_msg = f"License deactivation network error: {e}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def delete_license_locally(self):
        """Delete the license from local storage only (without server deactivation).

        Returns:
            bool: True if successful
        """
        try:
            self.set_license_key("")
            self.clear_cache()
            self.logger.info("License removed from local storage")
            return True
        except Exception as e:
            self.logger.error(f"Error removing license locally: {e}")
            return False
