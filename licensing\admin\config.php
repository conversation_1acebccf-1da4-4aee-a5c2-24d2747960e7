<?php
// Database configuration
$db_path = __DIR__ . '/../database/licenses.db';

// Create database connection
function getDatabase() {
    global $db_path;
    
    try {
        $pdo = new PDO('sqlite:' . $db_path);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        
        // Create tables if they don't exist
        createTables($pdo);
        
        return $pdo;
    } catch (PDOException $e) {
        die('Database connection failed: ' . $e->getMessage());
    }
}

// Create database tables
function createTables($pdo) {
    $sql = "
    -- Customers table
    CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        company TEXT,
        phone TEXT,
        created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended'))
    );

    -- License plans table
    CREATE TABLE IF NOT EXISTS license_plans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        plan_name TEXT UNIQUE NOT NULL,
        plan_type TEXT NOT NULL CHECK (plan_type IN ('free', 'monthly', 'yearly', 'lifetime')),
        price DECIMAL(10,2) DEFAULT 0.00,
        daily_image_limit INTEGER DEFAULT -1,
        features TEXT,
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_date DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- Licenses table
    CREATE TABLE IF NOT EXISTS licenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        license_key TEXT UNIQUE NOT NULL,
        customer_id INTEGER NOT NULL,
        plan_id INTEGER NOT NULL,
        activation_date DATETIME,
        expiry_date DATETIME,
        max_activations INTEGER DEFAULT 1,
        current_activations INTEGER DEFAULT 0,
        status TEXT DEFAULT 'inactive' CHECK (status IN ('active', 'inactive', 'expired', 'suspended', 'revoked')),
        created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        notes TEXT,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
        FOREIGN KEY (plan_id) REFERENCES license_plans (id) ON DELETE RESTRICT
    );

    -- Device activations table
    CREATE TABLE IF NOT EXISTS device_activations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        license_id INTEGER NOT NULL,
        device_id TEXT NOT NULL,
        device_name TEXT,
        device_info TEXT,
        activation_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
        FOREIGN KEY (license_id) REFERENCES licenses (id) ON DELETE CASCADE,
        UNIQUE(license_id, device_id)
    );

    -- Usage tracking table
    CREATE TABLE IF NOT EXISTS usage_tracking (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        license_id INTEGER NOT NULL,
        device_id TEXT NOT NULL,
        usage_date DATE NOT NULL,
        images_generated INTEGER DEFAULT 0,
        api_calls INTEGER DEFAULT 0,
        provider_used TEXT,
        model_used TEXT,
        created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (license_id) REFERENCES licenses (id) ON DELETE CASCADE,
        UNIQUE(license_id, device_id, usage_date)
    );
    ";
    
    $pdo->exec($sql);
    
    // Insert default license plans if they don't exist
    $plans_check = $pdo->query("SELECT COUNT(*) as count FROM license_plans")->fetch();
    if ($plans_check['count'] == 0) {
        $default_plans = "
        INSERT INTO license_plans (plan_name, plan_type, price, daily_image_limit, features, description) VALUES
        ('Free Plan', 'free', 0.00, 10, '{\"models\": [\"flux_schnell\"], \"providers\": [\"together_ai\"]}', 'Free plan with 10 images per day'),
        ('Monthly Pro', 'monthly', 29.99, -1, '{\"models\": [\"all\"], \"providers\": [\"all\"]}', 'Monthly subscription with unlimited images'),
        ('Yearly Pro', 'yearly', 299.99, -1, '{\"models\": [\"all\"], \"providers\": [\"all\"]}', 'Yearly subscription with unlimited images'),
        ('Lifetime Pro', 'lifetime', 999.99, -1, '{\"models\": [\"all\"], \"providers\": [\"all\"]}', 'Lifetime access to all features');
        ";
        $pdo->exec($default_plans);
    }
}

// Generate unique license key
function generateLicenseKey() {
    return 'BULKAI-' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 8)) . '-' . 
           strtoupper(substr(md5(uniqid(rand(), true)), 0, 8)) . '-' . 
           strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
}

// Check authentication
function checkAuth() {
    session_start();
    if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
        header('Location: index.php');
        exit;
    }
}

// Format date for display
function formatDate($date) {
    if (!$date) return 'N/A';
    return date('M j, Y g:i A', strtotime($date));
}

// Format currency
function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}

// Get status badge HTML
function getStatusBadge($status) {
    $class = '';
    switch ($status) {
        case 'active':
            $class = 'status active';
            break;
        case 'inactive':
            $class = 'status inactive';
            break;
        case 'expired':
            $class = 'status expired';
            break;
        default:
            $class = 'status';
    }
    return '<span class="' . $class . '">' . ucfirst($status) . '</span>';
}
?>
