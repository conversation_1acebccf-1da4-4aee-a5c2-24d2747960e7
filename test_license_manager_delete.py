#!/usr/bin/env python3
"""
Test script for license manager delete functionality.
This tests the desktop app's license manager methods directly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from license_manager import LicenseManager
from config_manager import Config<PERSON>ana<PERSON>

def test_license_manager_delete():
    """Test the license manager's delete functionality."""
    
    print("Testing License Manager Delete Functionality")
    print("=" * 60)
    
    # Create a test config manager
    config_manager = ConfigManager()
    
    # Create license manager
    license_manager = LicenseManager(config_manager)
    
    print(f"License Server URL: {license_manager.license_server_url}")
    print(f"Device ID: {license_manager.device_id}")
    
    # Test 1: Check initial license status
    print("\n1. Checking Initial License Status")
    print("-" * 40)
    
    initial_verification = license_manager.verify_license()
    print(f"Initial License Valid: {initial_verification.get('valid', False)}")
    
    if initial_verification.get('valid'):
        license_info = initial_verification.get('license', {})
        print(f"Plan: {license_info.get('plan_name', 'Unknown')}")
        print(f"License Key: {license_manager.get_license_key()}")
    else:
        print("No active license found")
    
    # Test 2: Activate a test license if none exists
    if not initial_verification.get('valid'):
        print("\n2. Activating Test License")
        print("-" * 40)
        
        test_license_key = "BULKAI-67BD2EA2-CE1423CF-40039D04"
        print(f"Activating license: {test_license_key}")
        
        result = license_manager.activate_license(test_license_key)
        print(f"Activation Success: {result.get('success', False)}")
        
        if result.get('success'):
            print("✅ License activated successfully")
        else:
            print(f"❌ License activation failed: {result.get('error', 'Unknown error')}")
            return
    
    # Test 3: Verify license is active
    print("\n3. Verifying Active License")
    print("-" * 40)
    
    verification = license_manager.verify_license()
    print(f"License Valid: {verification.get('valid', False)}")
    
    if verification.get('valid'):
        license_info = verification.get('license', {})
        print(f"Plan: {license_info.get('plan_name', 'Unknown')}")
        print(f"Plan Type: {license_info.get('plan_type', 'Unknown')}")
        
        usage_info = verification.get('usage', {})
        print(f"Today's Usage: {usage_info.get('today_usage', 0)}")
        
        device_info = verification.get('device', {})
        print(f"Device Activated: {device_info.get('activated', False)}")
        
        print("✅ License verification successful")
    else:
        print("❌ License verification failed")
        return
    
    # Test 4: Test server deactivation
    print("\n4. Testing Server Deactivation")
    print("-" * 40)
    
    deactivation_result = license_manager.deactivate_license()
    print(f"Deactivation Success: {deactivation_result.get('success', False)}")
    print(f"Deactivation Message: {deactivation_result.get('message', 'No message')}")
    
    if deactivation_result.get('success'):
        print("✅ Server deactivation successful")
        
        # Verify license is removed locally
        local_key = license_manager.get_license_key()
        print(f"Local License Key After Deactivation: '{local_key}'")
        
        if not local_key:
            print("✅ License key removed from local storage")
        else:
            print("❌ License key still present in local storage")
    else:
        print(f"❌ Server deactivation failed: {deactivation_result.get('error', 'Unknown error')}")
        
        # Test local deletion as fallback
        print("\n5. Testing Local License Deletion (Fallback)")
        print("-" * 40)
        
        local_delete_success = license_manager.delete_license_locally()
        print(f"Local Deletion Success: {local_delete_success}")
        
        if local_delete_success:
            print("✅ Local license deletion successful")
            
            # Verify license is removed locally
            local_key = license_manager.get_license_key()
            print(f"Local License Key After Local Deletion: '{local_key}'")
            
            if not local_key:
                print("✅ License key removed from local storage")
            else:
                print("❌ License key still present in local storage")
        else:
            print("❌ Local license deletion failed")
    
    # Test 6: Verify license is now inactive
    print("\n6. Verifying License is Inactive")
    print("-" * 40)
    
    final_verification = license_manager.verify_license()
    print(f"Final License Valid: {final_verification.get('valid', False)}")
    
    if not final_verification.get('valid'):
        print("✅ License successfully deactivated")
        print(f"Plan Type: {final_verification.get('plan_type', 'free')}")
    else:
        print("❌ License still appears to be active")
    
    # Test 7: Test plan status methods
    print("\n7. Testing Plan Status Methods")
    print("-" * 40)
    
    print(f"Is Trial Plan: {license_manager.is_trial_plan()}")
    print(f"Is Pro Plan: {license_manager.is_pro_plan()}")
    print(f"Can Access Bulk Generation: {license_manager.can_access_bulk_generation()}")
    print(f"Can Access Runware AI: {license_manager.can_access_runware_ai()}")
    print(f"Usage Status Text: {license_manager.get_usage_status_text()}")
    print(f"Plan Status Text: {license_manager.get_plan_status_text()}")
    
    print("\n" + "=" * 60)
    print("License Manager Delete Test Complete!")

if __name__ == "__main__":
    test_license_manager_delete()
