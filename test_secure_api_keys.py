#!/usr/bin/env python3
"""
Test script for the secure API key management system.

This script tests the secure storage functionality and API key migration.
"""

import sys
import os
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from secure_storage import SecureStorage
from config_manager import ConfigManager
from logger import get_logger


def test_secure_storage():
    """Test the secure storage functionality."""
    print("Testing Secure Storage System")
    print("=" * 50)
    
    # Initialize logger
    logger = get_logger()
    
    # Test secure storage
    storage = SecureStorage("BulkAI_Test")
    print(f"Storage method: {storage.storage_method}")
    
    # Test storing API keys
    test_services = {
        "together_ai": "test_together_key_12345",
        "runware_ai": "test_runware_key_67890"
    }
    
    print("\n1. Testing API key storage:")
    for service, key in test_services.items():
        success = storage.store_api_key(service, key)
        print(f"   Store {service}: {'✓' if success else '✗'}")
    
    # Test retrieving API keys
    print("\n2. Testing API key retrieval:")
    for service, expected_key in test_services.items():
        retrieved_key = storage.get_api_key(service)
        success = retrieved_key == expected_key
        print(f"   Retrieve {service}: {'✓' if success else '✗'}")
        if not success:
            print(f"      Expected: {expected_key}")
            print(f"      Got: {retrieved_key}")
    
    # Test listing services
    print("\n3. Testing service listing:")
    services = storage.list_stored_services()
    expected_services = set(test_services.keys())
    actual_services = set(services)
    success = expected_services.issubset(actual_services)
    print(f"   List services: {'✓' if success else '✗'}")
    print(f"   Services found: {services}")
    
    # Test deleting API keys
    print("\n4. Testing API key deletion:")
    for service in test_services.keys():
        success = storage.delete_api_key(service)
        print(f"   Delete {service}: {'✓' if success else '✗'}")
    
    # Verify deletion
    print("\n5. Verifying deletion:")
    for service in test_services.keys():
        retrieved_key = storage.get_api_key(service)
        success = retrieved_key is None or retrieved_key == ""
        print(f"   Verify {service} deleted: {'✓' if success else '✗'}")
    
    print("\nSecure storage test completed!")


def test_config_manager_integration():
    """Test the config manager integration with secure storage."""
    print("\n\nTesting Config Manager Integration")
    print("=" * 50)
    
    # Create a test config file with legacy API keys
    test_config_path = Path("test_config.json")
    legacy_config = {
        "api_keys": {
            "together_ai": "legacy_together_key_12345",
            "runware_ai": "legacy_runware_key_67890"
        },
        "settings": {
            "theme": "dark",
            "default_provider": "together_ai"
        }
    }
    
    # Write legacy config
    with open(test_config_path, 'w') as f:
        json.dump(legacy_config, f, indent=2)
    
    print("1. Created legacy config with API keys")
    
    # Initialize config manager (should migrate keys)
    config_manager = ConfigManager("test_config.json")
    
    # Test API key retrieval (should get from secure storage)
    print("\n2. Testing API key retrieval after migration:")
    for service in ["together_ai", "runware_ai"]:
        api_key = config_manager.get_api_key(service)
        expected_key = legacy_config["api_keys"][service]
        success = api_key == expected_key
        print(f"   Retrieve {service}: {'✓' if success else '✗'}")
        if not success:
            print(f"      Expected: {expected_key}")
            print(f"      Got: {api_key}")
    
    # Check that API keys were removed from config file
    print("\n3. Verifying API keys removed from config file:")
    with open(test_config_path, 'r') as f:
        updated_config = json.load(f)
    
    has_api_keys = "api_keys" in updated_config
    print(f"   Config file has api_keys section: {'✗' if not has_api_keys else '✓'}")
    
    # Test setting new API keys
    print("\n4. Testing new API key storage:")
    new_keys = {
        "together_ai": "new_together_key_99999",
        "runware_ai": "new_runware_key_88888"
    }
    
    for service, key in new_keys.items():
        success = config_manager.set_api_key(key, service)
        print(f"   Store new {service}: {'✓' if success else '✗'}")
    
    # Test retrieving new keys
    print("\n5. Testing new API key retrieval:")
    for service, expected_key in new_keys.items():
        retrieved_key = config_manager.get_api_key(service)
        success = retrieved_key == expected_key
        print(f"   Retrieve new {service}: {'✓' if success else '✗'}")
    
    # Test API key deletion
    print("\n6. Testing API key deletion:")
    for service in new_keys.keys():
        success = config_manager.delete_api_key(service)
        print(f"   Delete {service}: {'✓' if success else '✗'}")
    
    # Clean up
    if test_config_path.exists():
        test_config_path.unlink()
        print("\n7. Cleaned up test config file")
    
    print("\nConfig manager integration test completed!")


def main():
    """Run all tests."""
    print("BulkAI Secure API Key Management System Test")
    print("=" * 60)
    
    try:
        # Test secure storage
        test_secure_storage()
        
        # Test config manager integration
        test_config_manager_integration()
        
        print("\n" + "=" * 60)
        print("All tests completed successfully! ✓")
        print("\nThe secure API key management system is working correctly.")
        print("API keys will now be stored securely using your system's")
        print("secure storage mechanism instead of plain text config files.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
