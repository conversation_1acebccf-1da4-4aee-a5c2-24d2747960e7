<?php
/**
 * Setup script for BulkAI Licensing System
 * Run this script once to initialize the database and create default data
 */

require_once 'admin/config.php';

echo "<h1>BulkAI Licensing System Setup</h1>\n";

try {
    // Initialize database
    $pdo = getDatabase();
    echo "<p>✓ Database initialized successfully</p>\n";
    
    // Check if we already have data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customers");
    $customer_count = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM licenses");
    $license_count = $stmt->fetch()['count'];
    
    echo "<p>Current data:</p>\n";
    echo "<ul>\n";
    echo "<li>Customers: $customer_count</li>\n";
    echo "<li>Licenses: $license_count</li>\n";
    echo "</ul>\n";
    
    // Create sample data if none exists
    if ($customer_count == 0) {
        echo "<h2>Creating Sample Data</h2>\n";
        
        // Create sample customers
        $customers = [
            ['<PERSON> Doe', '<EMAIL>', 'Acme Corp', '******-0123'],
            ['<PERSON> <PERSON>', '<EMAIL>', 'Tech Solutions', '******-0456'],
            ['Bob Wilson', '<EMAIL>', '', '******-0789']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO customers (name, email, company, phone) VALUES (?, ?, ?, ?)");
        foreach ($customers as $customer) {
            $stmt->execute($customer);
        }
        echo "<p>✓ Created " . count($customers) . " sample customers</p>\n";
        
        // Create sample licenses
        $licenses = [
            [generateLicenseKey(), 1, 2, date('Y-m-d H:i:s', strtotime('+1 month')), 1, 'active'],  // Monthly
            [generateLicenseKey(), 2, 3, date('Y-m-d H:i:s', strtotime('+1 year')), 2, 'active'],   // Yearly
            [generateLicenseKey(), 3, 4, null, 1, 'active']  // Lifetime
        ];
        
        $stmt = $pdo->prepare("INSERT INTO licenses (license_key, customer_id, plan_id, expiry_date, max_activations, status) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($licenses as $license) {
            $stmt->execute($license);
        }
        echo "<p>✓ Created " . count($licenses) . " sample licenses</p>\n";
        
        // Display the sample license keys
        echo "<h3>Sample License Keys:</h3>\n";
        echo "<ul>\n";
        foreach ($licenses as $i => $license) {
            $plan_names = ['', '', 'Monthly Pro', 'Yearly Pro', 'Lifetime Pro'];
            echo "<li><strong>{$plan_names[$license[2]]}:</strong> <code>{$license[0]}</code></li>\n";
        }
        echo "</ul>\n";
    }
    
    // Display license plans
    $stmt = $pdo->query("SELECT * FROM license_plans ORDER BY id");
    $plans = $stmt->fetchAll();
    
    echo "<h2>Available License Plans</h2>\n";
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>Plan Name</th><th>Type</th><th>Price</th><th>Daily Limit</th><th>Features</th></tr>\n";
    foreach ($plans as $plan) {
        $daily_limit = $plan['daily_image_limit'] == -1 ? 'Unlimited' : $plan['daily_image_limit'];
        $price = '$' . number_format($plan['price'], 2);
        echo "<tr>";
        echo "<td>{$plan['id']}</td>";
        echo "<td>{$plan['plan_name']}</td>";
        echo "<td>" . ucfirst($plan['plan_type']) . "</td>";
        echo "<td>$price</td>";
        echo "<td>$daily_limit</td>";
        echo "<td>" . htmlspecialchars($plan['features']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<h2>Next Steps</h2>\n";
    echo "<ol>\n";
    echo "<li><strong>Change Admin Password:</strong> Edit the password in <code>admin/index.php</code></li>\n";
    echo "<li><strong>Configure License Server URL:</strong> Update the URL in your desktop application</li>\n";
    echo "<li><strong>Test API Endpoints:</strong> Use the sample license keys to test the API</li>\n";
    echo "<li><strong>Access Admin Panel:</strong> <a href='admin/'>Go to Admin Panel</a></li>\n";
    echo "</ol>\n";
    
    echo "<h2>API Endpoints</h2>\n";
    echo "<ul>\n";
    echo "<li><strong>Verify License:</strong> POST <code>api/index.php/verify-license</code></li>\n";
    echo "<li><strong>License Info:</strong> GET <code>api/index.php/license-info?license_key=KEY</code></li>\n";
    echo "<li><strong>Usage Tracking:</strong> POST <code>api/index.php/usage-tracking</code></li>\n";
    echo "<li><strong>Activate License:</strong> POST <code>api/index.php/activate-license</code></li>\n";
    echo "</ul>\n";
    
    echo "<h2>Test API</h2>\n";
    if (!empty($licenses)) {
        $test_key = $licenses[0][0];
        echo "<p>Test with sample license key: <code>$test_key</code></p>\n";
        echo "<pre>\n";
        echo "curl -X POST http://yourdomain.com/licensing/api/index.php/verify-license \\\n";
        echo "  -H \"Content-Type: application/json\" \\\n";
        echo "  -d '{\"license_key\":\"$test_key\",\"device_id\":\"test-device-123\"}'\n";
        echo "</pre>\n";
    }
    
    echo "<p><strong>Setup completed successfully!</strong></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 40px; }
table { margin: 20px 0; }
th { background-color: #f0f0f0; }
code { background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
pre { background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
</style>
